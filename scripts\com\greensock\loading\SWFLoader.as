package com.greensock.loading
{
   import com.greensock.events.LoaderEvent;
   import com.greensock.loading.core.DisplayObjectLoader;
   import com.greensock.loading.core.LoaderCore;
   import flash.display.AVM1Movie;
   import flash.display.DisplayObject;
   import flash.display.DisplayObjectContainer;
   import flash.display.MovieClip;
   import flash.events.Event;
   import flash.media.SoundTransform;
   import flash.utils.getQualifiedClassName;
   
   public class SWFLoader extends DisplayObjectLoader
   {
      private static var _classActivated:Boolean = _activateClass("SWFLoader",SWFLoader,"swf");
      
      protected var _queue:LoaderMax;
      
      protected var _hasRSL:Boolean;
      
      protected var _rslAddedCount:uint;
      
      protected var _loaderCompleted:Boolean;
      
      protected var _loadOnExitStealth:Boolean;
      
      protected var _loaderFailed:Boolean;
      
      public function SWFLoader(param1:*, param2:Object = null)
      {
         super(param1,param2);
         _preferEstimatedBytesInAudit = true;
         _type = "SWFLoader";
      }
      
      override protected function _load() : void
      {
         if(_stealthMode)
         {
            _stealthMode = _loadOnExitStealth;
         }
         else if(!_initted)
         {
            _loader.visible = false;
            _sprite.addChild(_loader);
            super._load();
         }
         else if(_queue != null)
         {
            _changeQueueListeners(true);
            _queue.load(false);
         }
      }
      
      override protected function _refreshLoader(param1:Boolean = true) : void
      {
         super._refreshLoader(param1);
         _loaderCompleted = false;
      }
      
      protected function _changeQueueListeners(param1:Boolean) : void
      {
         var _loc2_:String = null;
         if(_queue != null)
         {
            if(param1 && this.vars.integrateProgress != false)
            {
               _queue.addEventListener("complete",_completeHandler,false,0,true);
               _queue.addEventListener("progress",_progressHandler,false,0,true);
               _queue.addEventListener("fail",_failHandler,false,0,true);
               for(_loc2_ in _listenerTypes)
               {
                  if(_loc2_ != "onProgress" && _loc2_ != "onInit")
                  {
                     _queue.addEventListener(_listenerTypes[_loc2_],_passThroughEvent,false,0,true);
                  }
               }
            }
            else
            {
               _queue.removeEventListener("complete",_completeHandler);
               _queue.removeEventListener("progress",_progressHandler);
               _queue.removeEventListener("fail",_failHandler);
               for(_loc2_ in _listenerTypes)
               {
                  if(_loc2_ != "onProgress" && _loc2_ != "onInit")
                  {
                     _queue.removeEventListener(_listenerTypes[_loc2_],_passThroughEvent);
                  }
               }
            }
         }
      }
      
      override protected function _dump(param1:int = 0, param2:int = 0, param3:Boolean = false) : void
      {
         var _loc4_:* = undefined;
         _loaderCompleted = false;
         if(_status == 1 && !_initted && !_loaderFailed)
         {
            _stealthMode = true;
            super._dump(param1,param2,param3);
            return;
         }
         if(_initted && !_scriptAccessDenied && param1 != 2)
         {
            _stopMovieClips(_loader.content);
            if(_loader.content in _rootLookup)
            {
               _queue = LoaderMax(_rootLookup[_loader.content]);
               _changeQueueListeners(false);
               if(param1 == 0)
               {
                  _queue.cancel();
               }
               else
               {
                  delete _rootLookup[_loader.content];
                  _queue.dispose(param1 != 2);
               }
            }
         }
         if(param1 != 2 && _loader.parent == _sprite)
         {
            _sprite.removeChild(_loader);
         }
         if(_stealthMode)
         {
            try
            {
               _loader.close();
            }
            catch(error:Error)
            {
            }
         }
         _loadOnExitStealth = false;
         _stealthMode = _hasRSL = _loaderFailed = false;
         _cacheIsDirty = true;
         if(param1 >= 1)
         {
            _queue = null;
            _initted = false;
            super._dump(param1,param2,param3);
         }
         else
         {
            _loc4_ = _content;
            super._dump(param1,param2,param3);
            _content = _loc4_;
         }
      }
      
      protected function _stopMovieClips(param1:DisplayObject) : void
      {
         var _loc2_:MovieClip = param1 as MovieClip;
         if(_loc2_ == null)
         {
            return;
         }
         _loc2_.stop();
         var _loc3_:int = _loc2_.numChildren;
         while(true)
         {
            _loc3_--;
            if(_loc3_ <= -1)
            {
               break;
            }
            _stopMovieClips(_loc2_.getChildAt(_loc3_));
         }
      }
      
      override protected function _determineScriptAccess() : void
      {
         var _loc1_:DisplayObject = null;
         try
         {
            _loc1_ = _loader.content;
         }
         catch(error:Error)
         {
            _scriptAccessDenied = true;
            dispatchEvent(new LoaderEvent("scriptAccessDenied",this,error.message));
            return;
         }
         if(_loader.content is AVM1Movie)
         {
            _scriptAccessDenied = true;
            dispatchEvent(new LoaderEvent("scriptAccessDenied",this,"AVM1Movie denies script access"));
         }
      }
      
      override protected function _calculateProgress() : void
      {
         _cachedBytesLoaded = _stealthMode ? 0 : _loader.contentLoaderInfo.bytesLoaded;
         if(_loader.contentLoaderInfo.bytesTotal != 0)
         {
            _cachedBytesTotal = _loader.contentLoaderInfo.bytesTotal;
         }
         if(_cachedBytesTotal < _cachedBytesLoaded || _loaderCompleted)
         {
            _cachedBytesTotal = _cachedBytesLoaded;
         }
         if(this.vars.integrateProgress != false)
         {
            if(_queue != null && (uint(this.vars.estimatedBytes) < _cachedBytesLoaded || _queue.auditedSize))
            {
               if(_queue.status <= 2)
               {
                  _cachedBytesLoaded += _queue.bytesLoaded;
                  _cachedBytesTotal += _queue.bytesTotal;
               }
            }
            else if(uint(this.vars.estimatedBytes) > _cachedBytesLoaded && (!_initted || _queue != null && _queue.status <= 2 && !_queue.auditedSize))
            {
               _cachedBytesTotal = uint(this.vars.estimatedBytes);
            }
         }
         if(_hasRSL && _content == null || !_initted && _cachedBytesLoaded == _cachedBytesTotal)
         {
            _cachedBytesLoaded *= 0.99;
         }
         _cacheIsDirty = false;
      }
      
      protected function _checkRequiredLoaders() : void
      {
         if(_queue == null && this.vars.integrateProgress != false && !_scriptAccessDenied && _content != null)
         {
            _queue = _rootLookup[_content];
            if(_queue != null)
            {
               _changeQueueListeners(true);
               _queue.load(false);
               _cacheIsDirty = true;
            }
         }
      }
      
      public function getClass(param1:String) : Class
      {
         var _loc2_:Array = null;
         var _loc4_:int = 0;
         if(_content == null || _scriptAccessDenied)
         {
            return null;
         }
         var _loc3_:Object = !!_content.loaderInfo.applicationDomain.hasDefinition(param1) ? _content.loaderInfo.applicationDomain.getDefinition(param1) : null;
         if(_loc3_ != null)
         {
            return _loc3_ as Class;
         }
         if(_queue != null)
         {
            _loc2_ = _queue.getChildren(true,true);
            _loc4_ = int(_loc2_.length);
            while(true)
            {
               _loc4_--;
               if(_loc4_ <= -1)
               {
                  break;
               }
               if(_loc2_[_loc4_] is SWFLoader)
               {
                  _loc3_ = (_loc2_[_loc4_] as SWFLoader).getClass(param1);
                  if(_loc3_ != null)
                  {
                     return _loc3_ as Class;
                  }
               }
            }
         }
         return null;
      }
      
      public function getSWFChild(param1:String) : DisplayObject
      {
         return !_scriptAccessDenied && _content is DisplayObjectContainer ? DisplayObjectContainer(_content).getChildByName(param1) : null;
      }
      
      public function getLoader(param1:String) : *
      {
         return _queue != null ? _queue.getLoader(param1) : null;
      }
      
      public function getContent(param1:String) : *
      {
         if(param1 == this.name || param1 == _url)
         {
            return this.content;
         }
         var _loc2_:LoaderCore = this.getLoader(param1);
         return _loc2_ != null ? _loc2_.content : null;
      }
      
      public function getChildren(param1:Boolean = false, param2:Boolean = false) : Array
      {
         return _queue != null ? _queue.getChildren(param1,param2) : [];
      }
      
      override protected function _initHandler(param1:Event) : void
      {
         var _loc2_:Boolean = false;
         var _loc3_:DisplayObject = null;
         var _loc5_:String = null;
         var _loc4_:Object = null;
         if(_stealthMode)
         {
            _initted = true;
            _loc2_ = _loadOnExitStealth;
            _dump(1,_status,true);
            if(_loc2_)
            {
               _load();
            }
            return;
         }
         _hasRSL = false;
         try
         {
            _loc3_ = _loader.content;
            _loc5_ = getQualifiedClassName(_loc3_);
            if(_loc5_.substr(-13) == "__Preloader__")
            {
               _loc4_ = _loc3_["__rslPreloader"];
               if(_loc4_ != null)
               {
                  _loc5_ = getQualifiedClassName(_loc4_);
                  if(_loc5_ == "fl.rsl::RSLPreloader")
                  {
                     _hasRSL = true;
                     _rslAddedCount = 0;
                     _loc3_.addEventListener("added",_rslAddedHandler);
                  }
               }
            }
         }
         catch(error:Error)
         {
         }
         if(!_hasRSL)
         {
            _init();
         }
      }
      
      protected function _init() : void
      {
         var _loc1_:SoundTransform = null;
         _determineScriptAccess();
         if(!_scriptAccessDenied)
         {
            if(!_hasRSL)
            {
               _content = _loader.content;
            }
            if(_content != null)
            {
               if(this.vars.autoPlay == false && _content is MovieClip)
               {
                  _loc1_ = _content.soundTransform;
                  _loc1_.volume = 0;
                  _content.soundTransform = _loc1_;
                  _content.stop();
               }
               _checkRequiredLoaders();
            }
            if(_loader.parent == _sprite)
            {
               if(_sprite.stage != null && this.vars.suppressInitReparentEvents == true)
               {
                  _sprite.addEventListener("addedToStage",_captureFirstEvent,true,1000,true);
                  _loader.addEventListener("removedFromStage",_captureFirstEvent,true,1000,true);
               }
               _sprite.removeChild(_loader);
            }
         }
         else
         {
            _content = _loader;
            _loader.visible = true;
         }
         super._initHandler(null);
      }
      
      protected function _captureFirstEvent(param1:Event) : void
      {
         param1.stopImmediatePropagation();
         param1.currentTarget.removeEventListener(param1.type,_captureFirstEvent);
      }
      
      protected function _rslAddedHandler(param1:Event) : void
      {
         if(param1.target is DisplayObject && param1.currentTarget is DisplayObjectContainer && param1.target.parent == param1.currentTarget)
         {
            _rslAddedCount++;
         }
         if(_rslAddedCount > 1)
         {
            param1.currentTarget.removeEventListener("added",_rslAddedHandler);
            if(_status == 1)
            {
               _content = param1.target;
               _init();
               _calculateProgress();
               dispatchEvent(new LoaderEvent("progress",this));
               _completeHandler(null);
            }
         }
      }
      
      override protected function _passThroughEvent(param1:Event) : void
      {
         if(param1.target != _queue)
         {
            super._passThroughEvent(param1);
         }
      }
      
      override protected function _progressHandler(param1:Event) : void
      {
         var _loc2_:* = 0;
         var _loc3_:* = 0;
         if(_status == 1)
         {
            if(_queue == null && _initted)
            {
               _checkRequiredLoaders();
            }
            if(_dispatchProgress)
            {
               _loc2_ = _cachedBytesLoaded;
               _loc3_ = _cachedBytesTotal;
               _calculateProgress();
               if(_cachedBytesLoaded != _cachedBytesTotal && (_loc2_ != _cachedBytesLoaded || _loc3_ != _cachedBytesTotal))
               {
                  dispatchEvent(new LoaderEvent("progress",this));
               }
            }
            else
            {
               _cacheIsDirty = true;
            }
         }
      }
      
      override protected function _completeHandler(param1:Event = null) : void
      {
         var _loc2_:SoundTransform = null;
         _loaderCompleted = true;
         _checkRequiredLoaders();
         _calculateProgress();
         if(this.progress == 1)
         {
            if(!_scriptAccessDenied && this.vars.autoPlay == false && _content is MovieClip)
            {
               _loc2_ = _content.soundTransform;
               _loc2_.volume = 1;
               _content.soundTransform = _loc2_;
            }
            _changeQueueListeners(false);
            super._determineScriptAccess();
            super._completeHandler(param1);
         }
      }
      
      override protected function _failHandler(param1:Event) : void
      {
         if((param1.type == "ioError" || param1.type == "securityError") && param1.target == _loader.contentLoaderInfo)
         {
            _loaderFailed = true;
            if(_loadOnExitStealth)
            {
               _dump(1,_status,true);
               _load();
               return;
            }
         }
         super._failHandler(param1);
      }
      
      override public function set url(param1:String) : void
      {
         if(_url != param1)
         {
            if(_status == 1 && !_initted && !_loaderFailed)
            {
               _loadOnExitStealth = true;
            }
            super.url = param1;
         }
      }
   }
}

