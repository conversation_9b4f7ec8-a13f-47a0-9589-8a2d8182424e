package GM_UI
{
   import UI.AutomaticPetPanel.AutomaticPetPanel;
   import UI.GamingUI;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.XMLSingle;
   import YJFY.AutomaticPet.AutomaticPetVO;
   import YJFY.AutomaticPet.AutomaticPetsData;
   import YJFY.AutomaticPet.AutomaticSkillVO.AutomaticPetAuxiliarySkillVO;
   import YJFY.AutomaticPet.AutomaticSkillVO.AutomaticPetSkillVO;
   import YJFY.AutomaticPet.AutomaticSkillVO.AutomaticPetSkillVOFactory;
   import YJFY.AutomaticPet.AutomaticSkillVO.IAutomaticPetPassiveSkillVO;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.ButtonLogicShell;
   import YJFY.ShowLogicShell.ButtonLogicShell2;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   import flash.text.TextField;
   import flash.utils.setTimeout;
   
   public class AutomaticPetChange extends MySprite
   {
      private var m_show:MovieClip;
      
      private var m_showMC:MovieClipPlayLogicShell;
      
      private var m_changeCurrentAutoPetBtn:ButtonLogicShell2;
      
      private var m_changeBackAutoPetBtn:ButtonLogicShell2;
      
      private var m_deleCurrentAutoPetBtn:ButtonLogicShell2;
      
      private var m_addAutoPetBtn:ButtonLogicShell2;
      
      private var m_completeBtn:ButtonLogicShell2;
      
      private var m_completeBtn2:ButtonLogicShell2;
      
      private var m_autoPetNameText:TextField;
      
      private var m_autoPetLevelText:TextField;
      
      private var m_autoPetQualityText:TextField;
      
      private var m_autoPetSkillLvText:TextField;
      
      private var m_quitBtn:ButtonLogicShell;
      
      private var m_automaticPetPanel:AutomaticPetPanel;
      
      private var m_lebbdIDList:Vector.<TextField>;
      
      private var m_lebbdLvList:Vector.<TextField>;
      
      private var m_lebzdNameList:Vector.<TextField>;
      
      private var m_lebzdValueList:Vector.<TextField>;
      
      private var m_lebfzIDList:Vector.<TextField>;
      
      private var m_lebfzLvList:Vector.<TextField>;
      
      private var m_automaticPetsData:AutomaticPetsData;
      
      private var m_isBackAutoPetVO:Boolean;
      
      public function AutomaticPetChange()
      {
         super();
         addEventListener("clickButton",clickButton,true,0,true);
         this.initleb();
      }
      
      private function initleb() : void
      {
         m_lebbdIDList = new Vector.<TextField>();
         m_lebbdLvList = new Vector.<TextField>();
         m_lebzdNameList = new Vector.<TextField>();
         m_lebzdValueList = new Vector.<TextField>();
         m_lebfzIDList = new Vector.<TextField>();
         m_lebfzLvList = new Vector.<TextField>();
      }
      
      private function clearleb() : void
      {
         ClearUtil.clearObject(m_lebbdIDList);
         m_lebbdIDList.length = 0;
         m_lebbdIDList = null;
         ClearUtil.clearObject(m_lebbdLvList);
         m_lebbdLvList.length = 0;
         m_lebbdLvList = null;
         ClearUtil.clearObject(m_lebzdNameList);
         m_lebzdNameList.length = 0;
         m_lebzdNameList = null;
         ClearUtil.clearObject(m_lebzdValueList);
         m_lebzdValueList.length = 0;
         m_lebzdValueList = null;
         ClearUtil.clearObject(m_lebfzIDList);
         m_lebfzIDList.length = 0;
         m_lebfzIDList = null;
         ClearUtil.clearObject(m_lebfzLvList);
         m_lebfzLvList.length = 0;
         m_lebfzLvList = null;
      }
      
      override public function clear() : void
      {
         super.clear();
         this.clearleb();
      }
      
      public function init() : void
      {
         m_show = MyFunction2.returnShowByClassName("ChangeAutomaticPetPanel") as MovieClip;
         addChild(m_show);
         m_showMC = new MovieClipPlayLogicShell();
         m_showMC.setShow(m_show);
         m_automaticPetPanel = new AutomaticPetPanel();
         m_automaticPetPanel.setChangePanel(this);
         m_automaticPetsData = GamingUI.getInstance().getAutomaticPetsData();
         m_automaticPetPanel.init(m_automaticPetsData,null,null,null);
         m_show["automaticPetPanelContainer"].addChild(m_automaticPetPanel);
         initNormalFrame();
         setTimeout(function():*
         {
            m_changeCurrentAutoPetBtn = new ButtonLogicShell2();
            m_changeCurrentAutoPetBtn.setShow(m_show["changeCurrentAutoPetBtn"]);
            m_changeBackAutoPetBtn = new ButtonLogicShell2();
            m_changeBackAutoPetBtn.setShow(m_show["changeBackAutoPetBtn"]);
            m_deleCurrentAutoPetBtn = new ButtonLogicShell2();
            m_deleCurrentAutoPetBtn.setShow(m_show["deleCurrentAutoPetBtn"]);
            m_addAutoPetBtn = new ButtonLogicShell2();
            m_addAutoPetBtn.setShow(m_show["addAutoPetBtn"]);
            m_quitBtn = new ButtonLogicShell();
            m_quitBtn.setShow(m_show["quitBtn"]);
         },100);
      }
      
      public function closeAll() : void
      {
         if(parent)
         {
            parent.visible = false;
            this.visible = false;
            parent.removeChild(this);
         }
         clear();
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var currentAutomaticPetVO:AutomaticPetVO;
         var name:String;
         var automaticPetsXML:XML;
         var xmllist:XMLList;
         var i:int;
         var length:int;
         var xml:XML;
         var id:String;
         var level:uint;
         var newPetVO:AutomaticPetVO;
         var automaticPetBackVO:AutomaticPetVO;
         var curUid:String;
         var e:ButtonEvent = param1;
         loop1:
         switch(e.button)
         {
            case m_changeBackAutoPetBtn:
            case m_changeCurrentAutoPetBtn:
               if(e.button == m_changeBackAutoPetBtn)
               {
                  m_isBackAutoPetVO = true;
               }
               else
               {
                  m_isBackAutoPetVO = false;
               }
               currentAutomaticPetVO = m_automaticPetPanel.getCurrentAutomaticPetVO(m_isBackAutoPetVO);
               if(currentAutomaticPetVO == null)
               {
                  return;
               }
               initChangeAutoPetFrame();
               setTimeout(function():*
               {
                  m_autoPetNameText = m_show["nameTxt"];
                  m_autoPetLevelText = m_show["levelTxt"];
                  m_autoPetQualityText = m_show["qualityTxt"];
                  m_autoPetQualityText.type = "input";
                  m_autoPetLevelText.type = "input";
                  m_completeBtn = new ButtonLogicShell2();
                  m_completeBtn.setShow(m_show["completeBtn"]);
                  initLebList();
                  m_autoPetNameText.text = currentAutomaticPetVO.getName();
                  m_autoPetLevelText.text = currentAutomaticPetVO.getLevel().toString();
                  m_autoPetQualityText.text = currentAutomaticPetVO.getPingJieVO().getId().toString();
                  initlebInfo(currentAutomaticPetVO);
               },200);
               break;
            case m_deleCurrentAutoPetBtn:
               currentAutomaticPetVO = m_automaticPetPanel.getCurrentAutomaticPetVO();
               m_automaticPetsData.spliceAutomaticPetVO(currentAutomaticPetVO);
               ClearUtil.clearObject(m_automaticPetPanel);
               m_automaticPetPanel = new AutomaticPetPanel();
               m_automaticPetPanel.setChangePanel(this);
               m_automaticPetPanel.init(m_automaticPetsData,null,null,null);
               m_show["automaticPetPanelContainer"].addChild(m_automaticPetPanel);
               break;
            case m_addAutoPetBtn:
               initAddAutoPetFrame();
               setTimeout(function():*
               {
                  m_autoPetNameText = m_show["nameTxt"];
                  m_autoPetNameText.type = "input";
                  m_autoPetLevelText = m_show["levelTxt"];
                  m_autoPetLevelText.type = "input";
                  m_completeBtn2 = new ButtonLogicShell2();
                  m_completeBtn2.setShow(m_show["completeBtn2"]);
               },100);
               break;
            case m_completeBtn2:
               name = m_autoPetNameText.text;
               automaticPetsXML = XMLSingle.getInstance().automaticPetsXML;
               xmllist = automaticPetsXML.automaticPet;
               length = int(!!xmllist ? xmllist.length() : 0);
               i = 0;
               while(true)
               {
                  if(i >= length)
                  {
                     break loop1;
                  }
                  xml = xmllist[i];
                  if(xml.@name == name)
                  {
                     id = String(xml.@id);
                     level = uint(m_autoPetLevelText.text);
                     newPetVO = new AutomaticPetVO();
                     newPetVO.initFromXML(id,level,XMLSingle.getInstance().automaticPetsXML);
                     if(newPetVO.partnerName)
                     {
                        automaticPetBackVO = new AutomaticPetVO();
                        automaticPetBackVO.initFromXML(newPetVO.partnerName,level,XMLSingle.getInstance().automaticPetsXML);
                        curUid = String(m_automaticPetsData.addDoubleNumForUid());
                        newPetVO.partnerUid = curUid;
                        automaticPetBackVO.partnerUid = curUid;
                        m_automaticPetsData.addAutomaticPetVO(automaticPetBackVO);
                     }
                     m_automaticPetsData.addAutomaticPetVO(newPetVO);
                     m_automaticPetPanel.refreshShow(newPetVO);
                     clearFrame();
                     initNormalFrame();
                     setTimeout(function():*
                     {
                        m_changeCurrentAutoPetBtn = new ButtonLogicShell2();
                        m_changeCurrentAutoPetBtn.setShow(m_show["changeCurrentAutoPetBtn"]);
                        m_changeBackAutoPetBtn = new ButtonLogicShell2();
                        m_changeBackAutoPetBtn.setShow(m_show["changeBackAutoPetBtn"]);
                        m_deleCurrentAutoPetBtn = new ButtonLogicShell2();
                        m_deleCurrentAutoPetBtn.setShow(m_show["deleCurrentAutoPetBtn"]);
                        m_addAutoPetBtn = new ButtonLogicShell2();
                        m_addAutoPetBtn.setShow(m_show["addAutoPetBtn"]);
                        m_quitBtn = new ButtonLogicShell();
                        m_quitBtn.setShow(m_show["quitBtn"]);
                     },500);
                     break loop1;
                  }
                  ++i;
               }
               break;
            case m_quitBtn:
               clear();
               if(parent)
               {
                  parent.removeChild(this);
               }
               break;
            case m_completeBtn:
               changeComplete();
               clearFrame();
               initNormalFrame();
               setTimeout(function():*
               {
                  m_changeCurrentAutoPetBtn = new ButtonLogicShell2();
                  m_changeCurrentAutoPetBtn.setShow(m_show["changeCurrentAutoPetBtn"]);
                  m_changeBackAutoPetBtn = new ButtonLogicShell2();
                  m_changeBackAutoPetBtn.setShow(m_show["changeBackAutoPetBtn"]);
                  m_deleCurrentAutoPetBtn = new ButtonLogicShell2();
                  m_deleCurrentAutoPetBtn.setShow(m_show["deleCurrentAutoPetBtn"]);
                  m_addAutoPetBtn = new ButtonLogicShell2();
                  m_addAutoPetBtn.setShow(m_show["addAutoPetBtn"]);
                  m_quitBtn = new ButtonLogicShell();
                  m_quitBtn.setShow(m_show["quitBtn"]);
               },100);
         }
      }
      
      public function closecall() : void
      {
         if(parent)
         {
            this.visible = false;
            parent.removeChild(this);
         }
         clear();
      }
      
      private function changeComplete() : void
      {
         var _loc1_:* = 0;
         var _loc3_:AutomaticPetSkillVO = null;
         var _loc7_:int = 0;
         var _loc5_:int = 0;
         var _loc4_:AutomaticPetVO = null;
         var _loc6_:AutomaticPetSkillVO = null;
         var _loc2_:AutomaticPetSkillVOFactory = null;
         if(m_autoPetLevelText)
         {
            _loc1_ = uint(m_autoPetLevelText.text);
            m_automaticPetPanel.getCurrentAutomaticPetVO(m_isBackAutoPetVO).changeLevel(Math.min(m_automaticPetPanel.getCurrentAutomaticPetVO(m_isBackAutoPetVO).getMaxLevel(),Math.max(1,_loc1_)));
            m_automaticPetPanel.getCurrentAutomaticPetVO(m_isBackAutoPetVO).getPingJieVO().setId(m_autoPetQualityText.text);
            _loc7_ = 0;
            _loc5_ = 0;
            _loc4_ = m_automaticPetPanel.getCurrentAutomaticPetVO(m_isBackAutoPetVO);
            _loc5_ = int(_loc4_.getPassiveSkillVONum());
            _loc7_ = 0;
            while(_loc7_ < _loc5_)
            {
               _loc3_ = _loc4_.getPassiveSkillVOByIndex(_loc7_) as AutomaticPetSkillVO;
               if(_loc3_.getId() != this.m_lebbdIDList[_loc7_].text)
               {
                  _loc2_ = new AutomaticPetSkillVOFactory();
                  _loc6_ = _loc2_.createNewAutomaticPetSkillVO(this.m_lebbdIDList[_loc7_].text,uint(this.m_lebbdLvList[_loc7_].text));
                  ClearUtil.clearObject(_loc2_);
                  _loc2_ = null;
                  _loc4_.setPassiveSkillVOByIndex(_loc7_,_loc6_ as IAutomaticPetPassiveSkillVO);
               }
               else
               {
                  _loc3_.changeLevel(uint(this.m_lebbdLvList[_loc7_].text));
               }
               _loc7_++;
            }
            if(_loc5_ < 4)
            {
               while(_loc7_ < 4)
               {
                  if(this.m_lebbdIDList[_loc7_].text != "无")
                  {
                     _loc2_ = new AutomaticPetSkillVOFactory();
                     _loc6_ = _loc2_.createNewAutomaticPetSkillVO(this.m_lebbdIDList[_loc7_].text,uint(this.m_lebbdLvList[_loc7_].text));
                     ClearUtil.clearObject(_loc2_);
                     _loc2_ = null;
                     _loc4_.addPassiveSkillVO(_loc6_ as IAutomaticPetPassiveSkillVO);
                  }
                  _loc7_++;
               }
            }
            _loc5_ = int(_loc4_.getActiveSkillVONum());
            _loc7_ = 0;
            while(_loc7_ < _loc5_)
            {
               _loc3_ = _loc4_.getActiveSkillVOByIndex(_loc7_) as AutomaticPetSkillVO;
               _loc3_.changeLevel(int(this.m_lebzdValueList[_loc7_].text));
               trace("主动 等级," + this.m_lebzdValueList[_loc7_].text + _loc3_.getId() + _loc3_.getLevel());
               _loc7_++;
            }
            _loc5_ = int(_loc4_.getAuxiliarySkillVONum());
            _loc7_ = 0;
            while(_loc7_ < _loc5_)
            {
               _loc3_ = _loc4_.getAuxiliarySkillVOByIndex(_loc7_) as AutomaticPetSkillVO;
               _loc3_.changeLevel(int(this.m_lebfzLvList[_loc7_].text));
               trace("辅助 等级," + this.m_lebfzLvList[_loc7_].text + _loc3_.getId() + _loc3_.getLevel());
               _loc3_ = _loc4_.getAuxiliarySkillVOByIndex(_loc7_) as AutomaticPetSkillVO;
               if(_loc3_.getId() != this.m_lebfzIDList[_loc7_].text)
               {
                  _loc2_ = new AutomaticPetSkillVOFactory();
                  _loc6_ = _loc2_.createNewAutomaticPetSkillVO(this.m_lebfzIDList[_loc7_].text,uint(this.m_lebfzLvList[_loc7_].text));
                  ClearUtil.clearObject(_loc2_);
                  _loc2_ = null;
                  _loc4_.setAuxiliarySkillVOByIndex(_loc7_,_loc6_ as AutomaticPetAuxiliarySkillVO);
               }
               else
               {
                  _loc3_.changeLevel(uint(this.m_lebfzLvList[_loc7_].text));
               }
               _loc7_++;
            }
            if(_loc5_ < 4)
            {
               while(_loc7_ < 4)
               {
                  if(this.m_lebfzIDList[_loc7_].text != "无")
                  {
                     _loc2_ = new AutomaticPetSkillVOFactory();
                     _loc6_ = _loc2_.createNewAutomaticPetSkillVO(this.m_lebfzIDList[_loc7_].text,uint(this.m_lebfzLvList[_loc7_].text));
                     ClearUtil.clearObject(_loc2_);
                     _loc2_ = null;
                     _loc4_.setAuxiliarySkillVOByIndex(_loc7_,_loc6_ as AutomaticPetAuxiliarySkillVO);
                  }
                  _loc7_++;
               }
            }
         }
         m_automaticPetPanel.refreshShow(m_automaticPetPanel.getCurrentAutomaticPetVO());
      }
      
      private function clearFrame() : void
      {
         ClearUtil.clearObject(m_changeCurrentAutoPetBtn);
         m_changeCurrentAutoPetBtn = null;
         ClearUtil.clearObject(m_changeBackAutoPetBtn);
         m_changeBackAutoPetBtn = null;
         ClearUtil.clearObject(m_deleCurrentAutoPetBtn);
         m_deleCurrentAutoPetBtn = null;
         ClearUtil.clearObject(m_addAutoPetBtn);
         m_addAutoPetBtn = null;
         ClearUtil.clearObject(m_quitBtn);
         m_quitBtn = null;
         m_autoPetNameText = null;
         m_autoPetLevelText = null;
         ClearUtil.clearObject(m_completeBtn);
         m_completeBtn = null;
         ClearUtil.clearObject(m_completeBtn2);
         m_completeBtn2 = null;
      }
      
      private function initNormalFrame() : void
      {
         clearFrame();
         m_showMC.gotoAndStop("normal");
      }
      
      private function initChangeAutoPetFrame() : void
      {
         clearFrame();
         m_showMC.gotoAndStop("changeAutoPet");
      }
      
      private function initLebList() : void
      {
         var _loc1_:TextField = null;
         var _loc3_:int = 0;
         this.clearleb();
         this.initleb();
         var _loc2_:FangZhengKaTongJianTi = new FangZhengKaTongJianTi();
         if(this.m_lebbdIDList.length > 0)
         {
            return;
         }
         _loc3_ = 0;
         while(_loc3_ < 4)
         {
            _loc1_ = m_show["lebbdID_" + (_loc3_ + 1)];
            this.m_lebbdIDList.push(_loc1_);
            _loc1_.type = "input";
            _loc1_ = m_show["lebbdLv_" + (_loc3_ + 1)];
            this.m_lebbdLvList.push(_loc1_);
            _loc1_.type = "input";
            _loc1_ = m_show["lebzdName_" + (_loc3_ + 1)];
            _loc1_.text = "主动技能等级:";
            this.m_lebzdNameList.push(_loc1_);
            MyFunction2.changeTextFieldFont(_loc2_.fontName,_loc1_,true);
            _loc1_ = m_show["lebzdValue_" + (_loc3_ + 1)];
            this.m_lebzdValueList.push(_loc1_);
            _loc1_.type = "input";
            _loc1_ = m_show["lebfzID_" + (_loc3_ + 1)];
            this.m_lebfzIDList.push(_loc1_);
            _loc1_.type = "input";
            _loc1_ = m_show["lebfzLv_" + (_loc3_ + 1)];
            this.m_lebfzLvList.push(_loc1_);
            _loc1_.type = "input";
            _loc1_ = m_show["lebbdidtip_" + (_loc3_ + 1)];
            _loc1_.text = "被动技能槽" + (_loc3_ + 1) + "的id:";
            MyFunction2.changeTextFieldFont(_loc2_.fontName,_loc1_,true);
            _loc1_ = m_show["lebbdlvtip_" + (_loc3_ + 1)];
            _loc1_.text = "被动技能槽" + (_loc3_ + 1) + "的等级:";
            MyFunction2.changeTextFieldFont(_loc2_.fontName,_loc1_,true);
            _loc1_ = m_show["lebfzidtip_" + (_loc3_ + 1)];
            _loc1_.text = "辅助技能槽" + (_loc3_ + 1) + "的id:";
            MyFunction2.changeTextFieldFont(_loc2_.fontName,_loc1_,true);
            _loc1_ = m_show["lebfzlvtip_" + (_loc3_ + 1)];
            _loc1_.text = "辅助技能槽" + (_loc3_ + 1) + "的等级:";
            MyFunction2.changeTextFieldFont(_loc2_.fontName,_loc1_,true);
            _loc3_++;
         }
      }
      
      private function initlebInfo(param1:AutomaticPetVO) : void
      {
         var _loc3_:AutomaticPetSkillVO = null;
         var _loc5_:int = 0;
         var _loc4_:int = 0;
         _loc5_ = 0;
         while(_loc5_ < 4)
         {
            this.m_lebbdIDList[_loc5_].text = "无";
            this.m_lebzdNameList[_loc5_].text = "无";
            this.m_lebfzIDList[_loc5_].text = "无";
            this.m_lebbdLvList[_loc5_].text = "无";
            this.m_lebzdValueList[_loc5_].text = "无";
            this.m_lebfzLvList[_loc5_].text = "无";
            _loc5_++;
         }
         _loc4_ = int(param1.getPassiveSkillVONum());
         _loc5_ = 0;
         while(_loc5_ < _loc4_)
         {
            _loc3_ = param1.getPassiveSkillVOByIndex(_loc5_) as AutomaticPetSkillVO;
            this.m_lebbdIDList[_loc5_].text = _loc3_.getId();
            this.m_lebbdLvList[_loc5_].text = String(_loc3_.getLevel());
            trace("被动id:" + _loc3_.getId() + "等级 ：" + _loc3_.getLevel());
            _loc5_++;
         }
         _loc4_ = int(param1.getActiveSkillVONum());
         _loc5_ = 0;
         while(_loc5_ < _loc4_)
         {
            _loc3_ = param1.getActiveSkillVOByIndex(_loc5_) as AutomaticPetSkillVO;
            this.m_lebzdNameList[_loc5_].text = _loc3_.getName() + "的等级:";
            this.m_lebzdValueList[_loc5_].text = String(_loc3_.getLevel());
            trace("主动id:" + _loc3_.getId() + "等级 ：" + _loc3_.getLevel());
            _loc5_++;
         }
         _loc4_ = int(param1.getAuxiliarySkillVONum());
         _loc5_ = 0;
         while(_loc5_ < _loc4_)
         {
            _loc3_ = param1.getAuxiliarySkillVOByIndex(_loc5_) as AutomaticPetSkillVO;
            this.m_lebfzIDList[_loc5_].text = _loc3_.getId();
            this.m_lebfzLvList[_loc5_].text = String(_loc3_.getLevel());
            trace("辅助id:" + _loc3_.getId() + "等级 ：" + _loc3_.getLevel());
            _loc5_++;
         }
      }
      
      private function initAddAutoPetFrame() : void
      {
         clearFrame();
         m_showMC.gotoAndStop("addAutomaticPet");
      }
      
      public function delThis() : void
      {
         if(parent)
         {
            parent.removeChild(this);
         }
         clear();
      }
   }
}

