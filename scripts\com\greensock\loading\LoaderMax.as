package com.greensock.loading
{
   import com.greensock.events.LoaderEvent;
   import com.greensock.loading.core.LoaderCore;
   import com.greensock.loading.core.LoaderItem;
   import flash.display.DisplayObject;
   import flash.events.Event;
   import flash.net.URLRequest;
   import flash.system.LoaderContext;
   import flash.utils.Dictionary;
   
   public class LoaderMax extends LoaderCore
   {
      public static const version:Number = 1.776;
      
      public static var defaultEstimatedBytes:uint = 20000;
      
      public static var defaultAuditSize:Boolean = true;
      
      public static var defaultContext:LoaderContext;
      
      public static var contentDisplayClass:Class;
      
      protected var _loaders:Array;
      
      protected var _activeLoaders:Dictionary;
      
      public var skipFailed:Boolean;
      
      public var skipPaused:Boolean;
      
      public var maxConnections:uint;
      
      public function LoaderMax(param1:Object = null)
      {
         var _loc2_:int = 0;
         super(param1);
         _type = "LoaderMax";
         _loaders = [];
         _activeLoaders = new Dictionary();
         this.skipFailed = this.vars.skipFailed != false;
         this.skipPaused = this.vars.skipPaused != false;
         this.maxConnections = "maxConnections" in this.vars ? uint(this.vars.maxConnections) : 2;
         if(this.vars.loaders is Array)
         {
            _loc2_ = 0;
            while(_loc2_ < this.vars.loaders.length)
            {
               insert(this.vars.loaders[_loc2_],_loc2_);
               _loc2_++;
            }
         }
      }
      
      public static function parse(param1:*, param2:Object = null, param3:Object = null) : *
      {
         var _loc4_:LoaderMax = null;
         var _loc6_:int = 0;
         var _loc7_:int = 0;
         var _loc5_:String = null;
         if(param1 is Array)
         {
            _loc4_ = new LoaderMax(param2);
            _loc6_ = int(param1.length);
            _loc7_ = 0;
            while(_loc7_ < _loc6_)
            {
               _loc4_.append(LoaderMax.parse(param1[_loc7_],param3));
               _loc7_++;
            }
            return _loc4_;
         }
         if(param1 is String || param1 is URLRequest)
         {
            _loc5_ = param1 is String ? param1 : URLRequest(param1).url;
            _loc5_ = _loc5_.toLowerCase().split("?")[0];
            _loc5_ = _loc5_.substr(_loc5_.lastIndexOf(".") + 1);
            if(_loc5_ in _extensions)
            {
               return new _extensions[_loc5_](param1,param2);
            }
         }
         else if(param1 is LoaderCore)
         {
            return param1 as LoaderCore;
         }
         throw new Error("LoaderMax could not parse " + param1 + ". Don\'t forget to use LoaderMax.activate() to activate the necessary types of loaders.");
      }
      
      public static function activate(param1:Array) : void
      {
      }
      
      public static function getLoader(param1:String) : *
      {
         return _globalRootLoader != null ? _globalRootLoader.getLoader(param1) : null;
      }
      
      public static function getClass(param1:String) : Class
      {
         return _globalRootLoader != null ? _globalRootLoader.getClass(param1) : null;
      }
      
      public static function getContent(param1:String) : *
      {
         return _globalRootLoader != null ? _globalRootLoader.getContent(param1) : null;
      }
      
      public static function prioritize(param1:String, param2:Boolean = true) : LoaderCore
      {
         var _loc3_:LoaderCore = getLoader(param1);
         if(_loc3_ != null)
         {
            _loc3_.prioritize(param2);
         }
         return _loc3_;
      }
      
      override protected function _load() : void
      {
         _loadNext(null);
      }
      
      public function append(param1:LoaderCore) : LoaderCore
      {
         return insert(param1,_loaders.length);
      }
      
      public function prepend(param1:LoaderCore) : LoaderCore
      {
         return insert(param1,0);
      }
      
      public function insert(param1:LoaderCore, param2:uint = 999999999) : LoaderCore
      {
         if(param1 == null || param1 == this || _status == 5)
         {
            return null;
         }
         if(this != param1.rootLoader)
         {
            _removeLoader(param1,false);
         }
         param1.rootLoader.remove(param1);
         if(param2 > _loaders.length)
         {
            param2 = _loaders.length;
         }
         _loaders.splice(param2,0,param1);
         if(param1 != _globalRootLoader)
         {
            param1.addEventListener("progress",_progressHandler,false,0,true);
            param1.addEventListener("error",_errorHandler,false,0,true);
            param1.addEventListener("prioritize",_prioritizeHandler,false,0,true);
            for(var _loc3_ in _listenerTypes)
            {
               if(_loc3_ != "onProgress" && _loc3_ != "onInit")
               {
                  param1.addEventListener(_listenerTypes[_loc3_],_passThroughEvent,false,0,true);
               }
            }
         }
         param1.addEventListener("dispose",_disposeHandler,false,0,true);
         _cacheIsDirty = true;
         if(_status != 1)
         {
            if(_status != 3)
            {
               _status = 0;
            }
            else if(_prePauseStatus == 2)
            {
               _prePauseStatus = 0;
            }
         }
         return param1;
      }
      
      public function remove(param1:LoaderCore) : void
      {
         _removeLoader(param1,true);
      }
      
      protected function _removeLoader(param1:LoaderCore, param2:Boolean) : void
      {
         if(param1 == null)
         {
            return;
         }
         if(param2 && this != param1.rootLoader)
         {
            param1.rootLoader.append(param1);
         }
         _removeLoaderListeners(param1,true);
         _loaders.splice(getChildIndex(param1),1);
         if(param1 in _activeLoaders)
         {
            delete _activeLoaders[param1];
            param1.cancel();
            if(_status == 1)
            {
               _loadNext(null);
            }
         }
         _cacheIsDirty = true;
         _progressHandler(null);
      }
      
      public function empty(param1:Boolean = true, param2:Boolean = false) : void
      {
         var _loc3_:int = int(_loaders.length);
         while(true)
         {
            _loc3_--;
            if(_loc3_ <= -1)
            {
               break;
            }
            if(param1)
            {
               LoaderCore(_loaders[_loc3_]).dispose(param2);
            }
            if(param2)
            {
               if(LoaderCore(_loaders[_loc3_]))
               {
                  LoaderCore(_loaders[_loc3_]).unload();
               }
            }
            _removeLoader(_loaders[_loc3_],true);
         }
      }
      
      override protected function _dump(param1:int = 0, param2:int = 0, param3:Boolean = false) : void
      {
         var _loc4_:int = 0;
         if(param2 == 5)
         {
            _status = 5;
            empty(true,param1 == 3);
            if(this.vars.requireWithRoot is DisplayObject)
            {
               delete _rootLookup[this.vars.requireWithRoot];
            }
            _activeLoaders = null;
         }
         if(param1 <= 1)
         {
            _cancelActiveLoaders();
         }
         if(param1 == 1)
         {
            _loc4_ = int(_loaders.length);
            while(true)
            {
               _loc4_--;
               if(_loc4_ <= -1)
               {
                  break;
               }
               LoaderCore(_loaders[_loc4_]).unload();
            }
         }
         super._dump(param1,param2,param3);
         _cacheIsDirty = true;
      }
      
      override protected function _calculateProgress() : void
      {
         var _loc1_:int = 0;
         var _loc2_:LoaderCore = null;
         _cachedBytesLoaded = 0;
         _cachedBytesTotal = 0;
         var _loc3_:int = int(_loaders.length);
         while(true)
         {
            _loc3_--;
            if(_loc3_ <= -1)
            {
               break;
            }
            _loc2_ = _loaders[_loc3_];
            _loc1_ = _loc2_.status;
            if(_loc1_ <= 2 || !this.skipPaused && _loc1_ == 3 || !this.skipFailed && _loc1_ == 4)
            {
               _cachedBytesLoaded += _loc2_.bytesLoaded;
               _cachedBytesTotal += _loc2_.bytesTotal;
            }
         }
         _cacheIsDirty = false;
      }
      
      protected function _cancelActiveLoaders() : void
      {
         var _loc1_:LoaderCore = null;
         var _loc2_:int = int(_loaders.length);
         while(true)
         {
            _loc2_--;
            if(_loc2_ <= -1)
            {
               break;
            }
            _loc1_ = _loaders[_loc2_];
            if(_loc1_.status == 1)
            {
               delete _activeLoaders[_loc1_];
               _removeLoaderListeners(_loc1_,false);
               _loc1_.cancel();
            }
         }
      }
      
      protected function _removeLoaderListeners(param1:LoaderCore, param2:Boolean) : void
      {
         param1.removeEventListener("complete",_loadNext);
         param1.removeEventListener("cancel",_loadNext);
         if(param2)
         {
            param1.removeEventListener("progress",_progressHandler);
            param1.removeEventListener("prioritize",_prioritizeHandler);
            param1.removeEventListener("dispose",_disposeHandler);
            for(var _loc3_ in _listenerTypes)
            {
               if(_loc3_ != "onProgress" && _loc3_ != "onInit")
               {
                  param1.removeEventListener(_listenerTypes[_loc3_],_passThroughEvent);
               }
            }
         }
      }
      
      public function getChildrenByStatus(param1:int, param2:Boolean = false) : Array
      {
         var _loc6_:int = 0;
         var _loc4_:Array = [];
         var _loc3_:Array = getChildren(param2,false);
         var _loc5_:int = int(_loc3_.length);
         _loc6_ = 0;
         while(_loc6_ < _loc5_)
         {
            if(LoaderCore(_loc3_[_loc6_]).status == param1)
            {
               _loc4_.push(_loc3_[_loc6_]);
            }
            _loc6_++;
         }
         return _loc4_;
      }
      
      public function getChildren(param1:Boolean = false, param2:Boolean = false) : Array
      {
         var _loc5_:int = 0;
         var _loc3_:Array = [];
         var _loc4_:int = int(_loaders.length);
         _loc5_ = 0;
         while(_loc5_ < _loc4_)
         {
            if(!param2 || !(_loaders[_loc5_] is LoaderMax))
            {
               _loc3_.push(_loaders[_loc5_]);
            }
            if(param1 && _loaders[_loc5_].hasOwnProperty("getChildren"))
            {
               _loc3_ = _loc3_.concat(_loaders[_loc5_].getChildren(true,param2));
            }
            _loc5_++;
         }
         return _loc3_;
      }
      
      public function prependURLs(param1:String, param2:Boolean = false) : void
      {
         var _loc3_:Array = getChildren(param2,true);
         var _loc4_:int = int(_loc3_.length);
         while(true)
         {
            _loc4_--;
            if(_loc4_ <= -1)
            {
               break;
            }
            LoaderItem(_loc3_[_loc4_]).url = param1 + LoaderItem(_loc3_[_loc4_]).url;
         }
      }
      
      public function replaceURLText(param1:String, param2:String, param3:Boolean = false) : void
      {
         var _loc5_:LoaderItem = null;
         var _loc4_:Array = getChildren(param3,true);
         var _loc6_:int = int(_loc4_.length);
         while(true)
         {
            _loc6_--;
            if(_loc6_ <= -1)
            {
               break;
            }
            _loc5_ = _loc4_[_loc6_];
            _loc5_.url = _loc5_.url.split(param1).join(param2);
            if("alternateURL" in _loc5_.vars)
            {
               _loc5_.vars.alternateURL = _loc5_.vars.alternateURL.split(param1).join(param2);
            }
         }
      }
      
      public function getLoader(param1:String) : *
      {
         var _loc2_:LoaderCore = null;
         if(_loaders == null)
         {
            return null;
         }
         var _loc3_:int = int(_loaders.length);
         while(true)
         {
            _loc3_--;
            if(_loc3_ <= -1)
            {
               break;
            }
            _loc2_ = _loaders[_loc3_];
            if(_loc2_.name == param1 || _loc2_ is LoaderItem && (_loc2_ as LoaderItem).url == param1)
            {
               return _loc2_;
            }
            if(_loc2_.hasOwnProperty("getLoader"))
            {
               _loc2_ = (_loc2_ as Object).getLoader(param1) as LoaderCore;
               if(_loc2_ != null)
               {
                  return _loc2_;
               }
            }
         }
         return null;
      }
      
      public function getClass(param1:String) : Class
      {
         var _loc3_:LoaderCore = null;
         var _loc2_:Class = null;
         if(_loaders == null)
         {
            return null;
         }
         var _loc4_:int = int(_loaders.length);
         while(true)
         {
            _loc4_--;
            if(_loc4_ <= -1)
            {
               break;
            }
            _loc3_ = _loaders[_loc4_];
            if(_loc3_ is SWFLoader)
            {
               _loc2_ = (_loc3_ as SWFLoader).getClass(param1);
               if(_loc2_)
               {
                  return _loc2_;
               }
            }
            else if(_loc3_.hasOwnProperty("getClass"))
            {
               _loc2_ = (_loc3_ as Object).getClass(param1);
               if(_loc2_)
               {
                  return _loc2_;
               }
            }
         }
         return null;
      }
      
      public function getContent(param1:String) : *
      {
         var _loc2_:LoaderCore = this.getLoader(param1);
         return _loc2_ != null ? _loc2_.content : null;
      }
      
      public function getChildIndex(param1:LoaderCore) : uint
      {
         var _loc2_:int = int(_loaders.length);
         while(true)
         {
            _loc2_--;
            if(_loc2_ <= -1)
            {
               break;
            }
            if(_loaders[_loc2_] == param1)
            {
               return _loc2_;
            }
         }
         return 999999999;
      }
      
      override public function auditSize() : void
      {
         if(!this.auditedSize)
         {
            _auditSize(null);
         }
      }
      
      protected function _auditSize(param1:Event = null) : void
      {
         var _loc5_:Boolean = false;
         var _loc2_:LoaderCore = null;
         var _loc4_:int = 0;
         if(param1 != null)
         {
            param1.target.removeEventListener("auditedSize",_auditSize);
         }
         var _loc3_:uint = _loaders.length;
         var _loc6_:int = this.skipPaused ? 2 : 3;
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            _loc2_ = _loaders[_loc4_];
            if(!_loc2_.auditedSize && _loc2_.status <= _loc6_)
            {
               if(!_loc5_)
               {
                  _loc2_.addEventListener("auditedSize",_auditSize,false,0,true);
               }
               _loc5_ = true;
               _loc2_.auditSize();
            }
            _loc4_++;
         }
         if(!_loc5_)
         {
            if(_status == 1)
            {
               _loadNext(null);
            }
            dispatchEvent(new Event("auditedSize"));
         }
      }
      
      protected function _loadNext(param1:Event = null) : void
      {
         var _loc4_:Boolean = false;
         var _loc2_:LoaderCore = null;
         var _loc5_:* = 0;
         var _loc3_:* = 0;
         var _loc6_:int = 0;
         if(param1 && param1.type == "cancel")
         {
            return;
         }
         if(param1 != null && _activeLoaders != null)
         {
            delete _activeLoaders[param1.target];
            _removeLoaderListeners(LoaderCore(param1.target),false);
         }
         if(_status == 1)
         {
            _loc4_ = "auditSize" in this.vars ? Boolean(this.vars.auditSize) : LoaderMax.defaultAuditSize;
            if(_loc4_ && !this.auditedSize)
            {
               _auditSize(null);
               return;
            }
            _loc5_ = _loaders.length;
            _loc3_ = 0;
            _calculateProgress();
            _loc6_ = 0;
            while(_loc6_ < _loc5_)
            {
               _loc2_ = _loaders[_loc6_];
               if(!this.skipPaused && _loc2_.status == 3)
               {
                  super._failHandler(new LoaderEvent("fail",this,"Did not complete LoaderMax because skipPaused was false and " + _loc2_.toString() + " was paused."));
                  return;
               }
               if(!this.skipFailed && _loc2_.status == 4)
               {
                  super._failHandler(new LoaderEvent("fail",this,"Did not complete LoaderMax because skipFailed was false and " + _loc2_.toString() + " failed."));
                  return;
               }
               if(_loc2_.status <= 1)
               {
                  _loc3_++;
                  if(!(_loc2_ in _activeLoaders))
                  {
                     _activeLoaders[_loc2_] = true;
                     _loc2_.addEventListener("complete",_loadNext);
                     _loc2_.addEventListener("cancel",_loadNext);
                     _loc2_.load(false);
                  }
                  if(_loc3_ == this.maxConnections)
                  {
                     break;
                  }
               }
               _loc6_++;
            }
            if(_loc3_ == 0 && _cachedBytesLoaded == _cachedBytesTotal)
            {
               _completeHandler(null);
            }
         }
      }
      
      override protected function _progressHandler(param1:Event) : void
      {
         var _loc2_:* = 0;
         var _loc3_:* = 0;
         if(_dispatchProgress && _status != 5)
         {
            _loc2_ = _cachedBytesLoaded;
            _loc3_ = _cachedBytesTotal;
            _calculateProgress();
            if(!(_loc2_ == 0 && _cachedBytesLoaded == 0))
            {
               if((_cachedBytesLoaded != _cachedBytesTotal || _status != 1) && (_loc2_ != _cachedBytesLoaded || _loc3_ != _cachedBytesTotal))
               {
                  dispatchEvent(new LoaderEvent("progress",this));
               }
            }
         }
         else
         {
            _cacheIsDirty = true;
         }
         if(_dispatchChildProgress && param1 != null)
         {
            dispatchEvent(new LoaderEvent("childProgress",param1.target));
         }
      }
      
      protected function _disposeHandler(param1:Event) : void
      {
         _removeLoader(LoaderCore(param1.target),false);
      }
      
      protected function _prioritizeHandler(param1:Event) : void
      {
         var _loc3_:* = 0;
         var _loc2_:LoaderCore = param1.target as LoaderCore;
         _loaders.splice(getChildIndex(_loc2_),1);
         _loaders.unshift(_loc2_);
         if(_status == 1 && _loc2_.status <= 1 && !(_loc2_ in _activeLoaders))
         {
            _cancelActiveLoaders();
            _loc3_ = this.maxConnections;
            this.maxConnections = 1;
            _loadNext(null);
            this.maxConnections = _loc3_;
         }
      }
      
      public function get numChildren() : uint
      {
         return _loaders.length;
      }
      
      override public function get content() : *
      {
         var _loc1_:Array = [];
         var _loc2_:int = int(_loaders.length);
         while(true)
         {
            _loc2_--;
            if(_loc2_ <= -1)
            {
               break;
            }
            _loc1_[_loc2_] = LoaderCore(_loaders[_loc2_]).content;
         }
         return _loc1_;
      }
      
      override public function get status() : int
      {
         var _loc1_:Array = null;
         var _loc2_:int = 0;
         if(_status == 2)
         {
            _loc1_ = [0,0,0,0,0,0];
            _loc2_ = int(_loaders.length);
            while(true)
            {
               _loc2_--;
               if(_loc2_ <= -1)
               {
                  break;
               }
               _loc1_[LoaderCore(_loaders[_loc2_]).status]++;
            }
            if(!this.skipFailed && _loc1_[4] != 0 || !this.skipPaused && _loc1_[3] != 0)
            {
               _status = 4;
            }
            else if(_loc1_[0] + _loc1_[1] != 0)
            {
               _status = 0;
               _cacheIsDirty = true;
            }
         }
         return _status;
      }
      
      override public function get auditedSize() : Boolean
      {
         var _loc2_:int = this.skipPaused ? 2 : 3;
         var _loc1_:int = int(_loaders.length);
         while(true)
         {
            _loc1_--;
            if(_loc1_ <= -1)
            {
               break;
            }
            if(!LoaderCore(_loaders[_loc1_]).auditedSize && LoaderCore(_loaders[_loc1_]).status <= _loc2_)
            {
               return false;
            }
         }
         return true;
      }
      
      public function get rawProgress() : Number
      {
         var _loc3_:int = 0;
         var _loc2_:Number = 0;
         var _loc1_:Number = 0;
         var _loc4_:int = int(_loaders.length);
         while(true)
         {
            _loc4_--;
            if(_loc4_ <= -1)
            {
               break;
            }
            _loc3_ = int(LoaderCore(_loaders[_loc4_]).status);
            if(_loc3_ != 5 && !(_loc3_ == 3 && this.skipPaused) && !(_loc3_ == 4 && this.skipFailed))
            {
               _loc1_++;
               _loc2_ += LoaderCore(_loaders[_loc4_]).progress;
            }
         }
         return _loc1_ == 0 ? 0 : _loc2_ / _loc1_;
      }
   }
}

