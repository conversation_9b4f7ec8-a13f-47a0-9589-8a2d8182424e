package GM_UI
{
   import flash.net.SharedObject;
   
   public class GMData
   {
      private static var _instance:GMData = null;
      
      private var _isGMApplication:Boolean;
      
      public var originalXML:XML;
      
      public var saveXML:XML;
      
      public var startStr:String;
      
      public var saveIndex:int;
      
      public var changeNicknameDataXML:XML;
      
      public var newTaskXML:XML;
      
      public var newPKDataXML:XML;
      
      public var sharedObject:SharedObject;
      
      public function GMData()
      {
         super();
         if(!_instance)
         {
            _instance = this;
            return;
         }
         throw new Error("fuck you! 没看见实例已经存在么？！");
      }
      
      public static function getInstance() : GMData
      {
         if(_instance == null)
         {
            _instance = new GMData();
         }
         return _instance;
      }
      
      public function clear() : void
      {
         originalXML = null;
         saveXML = null;
         changeNicknameDataXML = null;
         newTaskXML = null;
         newPKDataXML = null;
         if(sharedObject)
         {
            sharedObject.clear();
         }
         sharedObject = null;
         _instance = null;
      }
      
      public function get isGMApplication() : Boolean
      {
         return _isGMApplication;
      }
      
      public function set isGMApplication(param1:Boolean) : void
      {
         _isGMApplication = param1;
      }
   }
}

