package UI.Animation.AnimationObjectByOneBitmap
{
   import flash.display.Bitmap;
   import flash.display.BitmapData;
   import flash.events.Event;
   import flash.events.TimerEvent;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import flash.utils.Timer;
   
   public class AnimationObjectByOneBitmap extends Bitmap
   {
      public static const PlAY_OVER_EVENT:String = "playerOverEvent";
      
      private var _animationData:IAnimationObjectDataByOneBitmap;
      
      private var _timer:Timer;
      
      private var _currentLocation:Number;
      
      public function AnimationObjectByOneBitmap(param1:BitmapData = null, param2:String = "auto", param3:Boolean = false)
      {
         super(param1,param2,param3);
         _timer = new Timer(0,0);
         _timer.addEventListener("timer",onTimer,false,0,true);
         _timer.addEventListener("timerComplete",clearAnimationObject,false,0,true);
      }
      
      public function set animationData(param1:IAnimationObjectDataByOneBitmap) : void
      {
         _animationData = param1;
         _timer.delay = param1.delay;
         _currentLocation = 0;
      }
      
      public function clear() : void
      {
         if(_timer)
         {
            _timer.removeEventListener("timer",onTimer,false);
            _timer.removeEventListener("timerComplete",clearAnimationObject,false);
         }
         if(_timer)
         {
            _timer.stop();
         }
         _timer = null;
         _animationData = null;
      }
      
      public function play() : void
      {
         _timer.reset();
         _timer.start();
         onTimer(null);
      }
      
      public function stop() : void
      {
         _timer.stop();
      }
      
      private function onTimer(param1:TimerEvent) : void
      {
         var _loc3_:Number = NaN;
         var _loc4_:Number = NaN;
         var _loc2_:BitmapData = new BitmapData(_animationData.width,_animationData.height,true,0);
         if(this.bitmapData)
         {
            this.bitmapData.dispose();
         }
         if(_currentLocation + _animationData.width <= _animationData.sourceBitmapData.width)
         {
            _loc2_.copyPixels(_animationData.sourceBitmapData,new Rectangle(_currentLocation,0,_animationData.width,_animationData.height),new Point(0,0));
            bitmapData = _loc2_;
         }
         else
         {
            _loc3_ = _animationData.sourceBitmapData.width - _currentLocation;
            _loc4_ = _currentLocation + _animationData.width - _animationData.sourceBitmapData.width;
            _loc2_.copyPixels(_animationData.sourceBitmapData,new Rectangle(_currentLocation,0,_loc3_,_animationData.height),new Point(0,0));
            _loc2_.copyPixels(_animationData.sourceBitmapData,new Rectangle(0,0,_loc4_,_animationData.height),new Point(_loc3_,0));
            bitmapData = _loc2_;
         }
         _currentLocation += _animationData.moveDistance * _animationData.direction;
         if(_currentLocation > _animationData.sourceBitmapData.width)
         {
            _currentLocation -= _animationData.sourceBitmapData.width;
         }
         else if(_currentLocation < 0)
         {
            _currentLocation = _animationData.sourceBitmapData.width + _currentLocation;
         }
      }
      
      private function clearAnimationObject(param1:TimerEvent) : void
      {
         _timer.removeEventListener("timer",onTimer,false);
         _timer.removeEventListener("timerComplete",clearAnimationObject,false);
         dispatchEvent(new Event("playerOverEvent"));
         _animationData = null;
         _timer = null;
         if(this.parent)
         {
            parent.removeChild(this);
         }
      }
   }
}

