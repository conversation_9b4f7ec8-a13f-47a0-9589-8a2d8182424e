package UI.AutomaticPetPanel
{
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI2.broadcast.SubmitFunction;
   import YJFY.AutomaticPet.AutomaticPetVO;
   import YJFY.AutomaticPet.AutomaticPetsData;
   import YJFY.EntityShowContainer;
   import YJFY.MiddleWord;
   import YJFY.Part1;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.AnimationShowPlayLogicShell;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.StopListener;
   import YJFY.ShowLogicShell.NumShowLogicShell.MultiPlaceNumLogicShell;
   import YJFY.Utils.ClearUtil;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.text.TextField;
   
   public class StrengthenPanel
   {
      private var m_petNameText:TextField;
      
      private var m_currentLevelShow:MultiPlaceNumLogicShell;
      
      private var m_nextLevelShow:MultiPlaceNumLogicShell;
      
      private var m_attrShowMC:MovieClipPlayLogicShell;
      
      private var m_hpText:TextField;
      
      private var m_mpText:TextField;
      
      private var m_attackText:TextField;
      
      private var m_defenceText:TextField;
      
      private var m_hitRateText:TextField;
      
      private var m_dogdeRateText:TextField;
      
      private var m_criticalRateText:TextField;
      
      private var m_decriticalRateText:TextField;
      
      private var m_addHpText:TextField;
      
      private var m_addMpText:TextField;
      
      private var m_addAttackText:TextField;
      
      private var m_addDefenceText:TextField;
      
      private var m_addHitRateText:TextField;
      
      private var m_addDogdeRateText:TextField;
      
      private var m_addCriticalRateText:TextField;
      
      private var m_addDecriticalRateText:TextField;
      
      private var m_addBtns:Vector.<AddDevourAndShowBtn>;
      
      private var m_startBtn:ButtonLogicShell2;
      
      private var m_showAnimationContianer:EntityShowContainer;
      
      private var m_nextLevelAutomaticPetVO:AutomaticPetVO;
      
      private var m_choiceDevourPanel:ChoiceDevourPanel;
      
      private var m_font:FangZhengKaTongJianTi;
      
      private var m_choiceAutomaticPetVOs:Vector.<AutomaticPetVO>;
      
      private var m_mainDevourAnimation:AnimationShowPlayLogicShell;
      
      private var m_devourAnimationStopListener:StopListener;
      
      private var m_devourAnimations:Vector.<AnimationShowPlayLogicShell>;
      
      private var m_show:MovieClip;
      
      private var m_showContainer:MovieClip;
      
      private var m_automaticPetVO:AutomaticPetVO;
      
      private var m_automaticPetsData:AutomaticPetsData;
      
      private var m_automaticPetPanel:AutomaticPetPanel;
      
      private var m_preSkinBtn:ButtonLogicShell2;
      
      private var m_nextSkinBtn:ButtonLogicShell2;
      
      private var forceShowOneVO:AutomaticPetVO;
      
      public function StrengthenPanel()
      {
         super();
         m_showAnimationContianer = new EntityShowContainer();
         m_showAnimationContianer.init();
         m_addBtns = new Vector.<AddDevourAndShowBtn>();
         m_startBtn = new ButtonLogicShell2();
         m_attrShowMC = new MovieClipPlayLogicShell();
         m_font = new FangZhengKaTongJianTi();
         m_mainDevourAnimation = new AnimationShowPlayLogicShell();
         m_devourAnimationStopListener = new StopListener();
         m_devourAnimationStopListener.stop2Fun = devourAnimatonStop;
         m_mainDevourAnimation.addNextStopListener(m_devourAnimationStopListener);
         m_devourAnimations = new Vector.<AnimationShowPlayLogicShell>();
         m_choiceAutomaticPetVOs = new Vector.<AutomaticPetVO>();
         m_preSkinBtn = new ButtonLogicShell2();
         m_nextSkinBtn = new ButtonLogicShell2();
      }
      
      public function clear() : void
      {
         if(m_show)
         {
            m_show.removeEventListener("clickButton",clickButton,true);
         }
         m_petNameText = null;
         ClearUtil.clearObject(m_currentLevelShow);
         m_currentLevelShow = null;
         ClearUtil.clearObject(m_nextLevelShow);
         m_nextLevelShow = null;
         ClearUtil.clearObject(m_attrShowMC);
         m_attrShowMC = null;
         m_hpText = null;
         m_mpText = null;
         m_attackText = null;
         m_defenceText = null;
         m_hitRateText = null;
         m_dogdeRateText = null;
         m_criticalRateText = null;
         m_decriticalRateText = null;
         ClearUtil.clearObject(m_font);
         m_font = null;
         m_addHpText = null;
         m_addMpText = null;
         m_addAttackText = null;
         m_addDefenceText = null;
         m_addHitRateText = null;
         m_addDogdeRateText = null;
         m_addCriticalRateText = null;
         m_addDecriticalRateText = null;
         ClearUtil.clearObject(m_addBtns);
         m_addBtns = null;
         ClearUtil.clearObject(m_startBtn);
         m_startBtn = null;
         ClearUtil.clearObject(m_showAnimationContianer);
         m_showAnimationContianer = null;
         ClearUtil.clearObject(m_nextLevelAutomaticPetVO);
         m_nextLevelAutomaticPetVO = null;
         ClearUtil.clearObject(m_choiceDevourPanel);
         m_choiceDevourPanel = null;
         ClearUtil.clearObject(m_mainDevourAnimation);
         m_mainDevourAnimation = null;
         ClearUtil.clearObject(m_devourAnimationStopListener);
         m_devourAnimationStopListener = null;
         ClearUtil.clearObject(m_devourAnimations);
         m_devourAnimations = null;
         ClearUtil.nullArr(m_choiceAutomaticPetVOs,false,false,false);
         m_choiceAutomaticPetVOs = null;
         ClearUtil.clearObject(m_preSkinBtn);
         m_preSkinBtn = null;
         ClearUtil.clearObject(m_nextSkinBtn);
         m_nextSkinBtn = null;
         m_show = null;
         m_showContainer = null;
         m_automaticPetVO = null;
         m_automaticPetsData = null;
         m_automaticPetPanel = null;
      }
      
      public function setShow(param1:MovieClip, param2:AutomaticPetPanel) : void
      {
         m_show = param1;
         m_show.addEventListener("clickButton",clickButton,true,0,true);
         m_automaticPetPanel = param2;
         initShow();
         initShow2();
      }
      
      public function setData(param1:AutomaticPetVO, param2:AutomaticPetsData) : void
      {
         if(forceShowOneVO && forceShowOneVO.partnerUid && forceShowOneVO.partnerUid == m_automaticPetVO.partnerUid)
         {
            m_automaticPetVO = forceShowOneVO;
         }
         else
         {
            m_automaticPetVO = param1;
         }
         forceShowOneVO = null;
         m_automaticPetsData = param2;
         initShow2();
      }
      
      private function initShow2() : void
      {
         if(m_automaticPetVO == null)
         {
            return;
         }
         m_petNameText.text = m_automaticPetVO.getName();
         m_showContainer.addChild(m_showAnimationContianer.getShow());
         m_showAnimationContianer.refreshAutomaticPetShow(m_automaticPetVO);
         empyAddBtns();
         if(getAbleGetExpFromDevourPets() && m_automaticPetVO.getLevel() < m_automaticPetVO.getMaxLevel())
         {
            initAttributePartTwoFrame();
         }
         else
         {
            initAttributePartOneFrame();
         }
         if(m_automaticPetVO.partnerUid)
         {
            m_nextSkinBtn.getShow().visible = true;
            m_preSkinBtn.getShow().visible = true;
         }
         else
         {
            m_nextSkinBtn.getShow().visible = false;
            m_preSkinBtn.getShow().visible = false;
         }
      }
      
      private function initShow() : void
      {
         var _loc7_:int = 0;
         var _loc6_:int = 0;
         var _loc5_:int = 0;
         var _loc2_:DisplayObject = null;
         var _loc4_:AddDevourAndShowBtn = null;
         var _loc1_:AnimationShowPlayLogicShell = null;
         m_petNameText = m_show["petNameText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_petNameText);
         m_showContainer = m_show["showContainer"];
         m_attrShowMC.setShow(m_show["attriShow"]);
         m_mainDevourAnimation.setShow(m_show["mainDevourAnimation"]);
         m_mainDevourAnimation.getDisplayShow().visible = false;
         var _loc3_:int = m_show.numChildren;
         _loc7_ = 0;
         while(_loc7_ < _loc3_)
         {
            _loc2_ = m_show.getChildAt(_loc7_);
            if(_loc2_.name.substr(0,7) == "addBtn_")
            {
               _loc6_++;
            }
            else if(_loc2_.name.substr(0,16) == "devourAnimation_")
            {
               _loc5_++;
            }
            _loc7_++;
         }
         _loc7_ = 0;
         while(_loc7_ < _loc6_)
         {
            _loc4_ = new AddDevourAndShowBtn();
            _loc4_.setShow(m_show["addBtn_" + (_loc7_ + 1)]);
            m_addBtns.push(_loc4_);
            _loc7_++;
         }
         _loc7_ = 0;
         while(_loc7_ < _loc5_)
         {
            _loc1_ = new AnimationShowPlayLogicShell();
            _loc1_.addNextStopListener(m_devourAnimationStopListener);
            _loc1_.setShow(m_show["devourAnimation_" + (_loc7_ + 1)]);
            m_devourAnimations.push(_loc1_);
            _loc1_.getDisplayShow().visible = false;
            _loc7_++;
         }
         m_startBtn.setShow(m_show["startBtn"]);
         m_nextSkinBtn.setShow(m_show["btnNextSkin"]);
         m_nextSkinBtn.setTipString("点击切换妖将");
         m_preSkinBtn.setShow(m_show["btnPreSkin"]);
         m_preSkinBtn.setTipString("点击切换妖将");
      }
      
      private function initAttributePartOneFrame() : void
      {
         clearAttributePartFrame();
         m_attrShowMC.gotoAndStop("unableUpGrade");
         initAttributePartFrme();
         m_currentLevelShow = new MultiPlaceNumLogicShell();
         m_currentLevelShow.setShow(m_attrShowMC.getShow()["currentLevelShow"]);
         if(m_automaticPetVO == null)
         {
            return;
         }
         m_currentLevelShow.showNum(m_automaticPetVO.getLevel());
      }
      
      private function initAttributePartTwoFrame() : void
      {
         if(m_automaticPetVO == null)
         {
            throw new Error("出错了");
         }
         clearAttributePartFrame();
         m_attrShowMC.gotoAndStop("ableUpgrade");
         initAttributePartFrme();
         m_addHpText = m_attrShowMC.getShow()["addHpText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_addHpText);
         m_addMpText = m_attrShowMC.getShow()["addMpText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_addMpText);
         m_addAttackText = m_attrShowMC.getShow()["addAttackText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_addAttackText);
         m_addDefenceText = m_attrShowMC.getShow()["addDefenceText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_addDefenceText);
         m_addHitRateText = m_attrShowMC.getShow()["addHitRateText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_addHitRateText);
         m_addDogdeRateText = m_attrShowMC.getShow()["addDogdeRateText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_addDogdeRateText);
         m_addCriticalRateText = m_attrShowMC.getShow()["addCriticalRateText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_addCriticalRateText);
         m_addDecriticalRateText = m_attrShowMC.getShow()["addDecriticalRateText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_addDecriticalRateText);
         m_currentLevelShow = new MultiPlaceNumLogicShell();
         m_currentLevelShow.setShow(m_attrShowMC.getShow()["currentLevelShow"]);
         m_nextLevelShow = new MultiPlaceNumLogicShell();
         m_nextLevelShow.setShow(m_attrShowMC.getShow()["nextLevelShow"]);
         if(m_automaticPetVO == null)
         {
            return;
         }
         createNewNextLevelAutomatiPetVO(getAbleGetExpFromDevourPets());
         m_addHpText.text = "+" + (m_nextLevelAutomaticPetVO.getTotalHp() - m_automaticPetVO.getTotalHp());
         m_addMpText.text = "+" + (m_nextLevelAutomaticPetVO.getTotalMp() - m_automaticPetVO.getTotalMp());
         m_addAttackText.text = "+" + (m_nextLevelAutomaticPetVO.getAttack() - m_automaticPetVO.getAttack());
         m_addDefenceText.text = "+" + (m_nextLevelAutomaticPetVO.getDefence() - m_automaticPetVO.getDefence());
         m_addHitRateText.text = "+" + ((m_nextLevelAutomaticPetVO.getHitRate() - m_automaticPetVO.getHitRate()) * 100).toFixed(2) + "%";
         m_addDogdeRateText.text = "+" + ((m_nextLevelAutomaticPetVO.getDogdeRate() - m_automaticPetVO.getDogdeRate()) * 100).toFixed(2) + "%";
         m_addCriticalRateText.text = "+" + ((m_nextLevelAutomaticPetVO.getCriticalRate() - m_automaticPetVO.getCriticalRate()) * 100).toFixed(2) + "%";
         m_addDecriticalRateText.text = "+" + ((m_nextLevelAutomaticPetVO.getDeCriticalRate() - m_automaticPetVO.getDeCriticalRate()) * 100).toFixed(2) + "%";
         m_currentLevelShow.showNum(m_automaticPetVO.getLevel());
         m_nextLevelShow.showNum(m_nextLevelAutomaticPetVO.getLevel());
      }
      
      private function initAttributePartFrme() : void
      {
         m_hpText = m_attrShowMC.getShow()["hpText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_hpText);
         m_mpText = m_attrShowMC.getShow()["mpText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_mpText);
         m_attackText = m_attrShowMC.getShow()["attackText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_attackText);
         m_defenceText = m_attrShowMC.getShow()["defenceText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_defenceText);
         m_hitRateText = m_attrShowMC.getShow()["hitRateText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_hitRateText);
         m_dogdeRateText = m_attrShowMC.getShow()["dogdeRateText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_dogdeRateText);
         m_criticalRateText = m_attrShowMC.getShow()["criticalRateText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_criticalRateText);
         m_decriticalRateText = m_attrShowMC.getShow()["decriticalRateText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_decriticalRateText);
         m_hpText.text = m_automaticPetVO.getTotalHp().toString();
         m_mpText.text = m_automaticPetVO.getTotalMp().toString();
         m_attackText.text = m_automaticPetVO.getAttack().toString();
         m_defenceText.text = m_automaticPetVO.getDefence().toString();
         m_hitRateText.text = (m_automaticPetVO.getHitRate() * 100).toFixed(2) + "%";
         m_dogdeRateText.text = (m_automaticPetVO.getDogdeRate() * 100).toFixed(2) + "%";
         m_criticalRateText.text = (m_automaticPetVO.getCriticalRate() * 100).toFixed(2) + "%";
         m_decriticalRateText.text = (m_automaticPetVO.getDeCriticalRate() * 100).toFixed(2) + "%";
      }
      
      private function clearAttributePartFrame() : void
      {
         m_hpText = null;
         m_mpText = null;
         m_attackText = null;
         m_defenceText = null;
         m_hitRateText = null;
         m_dogdeRateText = null;
         m_criticalRateText = null;
         m_decriticalRateText = null;
         ClearUtil.clearObject(m_currentLevelShow);
         m_currentLevelShow = null;
         ClearUtil.clearObject(m_nextLevelShow);
         m_nextLevelShow = null;
      }
      
      private function createNewNextLevelAutomatiPetVO(param1:uint) : void
      {
         ClearUtil.clearObject(m_nextLevelAutomaticPetVO);
         m_nextLevelAutomaticPetVO = new AutomaticPetVO();
         m_nextLevelAutomaticPetVO.copy(m_automaticPetVO);
         m_nextLevelAutomaticPetVO.addExp(param1);
         m_nextLevelAutomaticPetVO.changeData();
      }
      
      private function getAbleGetExpFromDevourPets() : uint
      {
         var _loc1_:* = 0;
         var _loc3_:int = 0;
         var _loc2_:int = int(m_choiceAutomaticPetVOs.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            _loc1_ += m_choiceAutomaticPetVOs[_loc3_].getDevourExp();
            _loc3_++;
         }
         return _loc1_;
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var _loc3_:int = 0;
         switch(param1.button)
         {
            case m_startBtn:
               startDevour();
               var _loc2_:int = !!m_addBtns ? m_addBtns.length : 0;
               _loc3_ = 0;
               §§goto(addr38);
            case m_nextSkinBtn:
            case m_preSkinBtn:
               break;
            default:
               addr38:
               while(_loc3_ < _loc2_)
               {
                  if(m_addBtns[_loc3_] == param1.button)
                  {
                     if(m_automaticPetVO.getLevel() < m_automaticPetVO.getMaxLevel())
                     {
                        openChoiceDevourPanel();
                     }
                     else
                     {
                        m_automaticPetPanel.showWarningBox("已是最大等级",0);
                     }
                     return;
                  }
                  _loc3_++;
               }
               return;
         }
         setData(GamingUI.getInstance().getAutomaticPetsData().getBackPetVoByUid(m_automaticPetVO.partnerUid,m_automaticPetVO.partnerName),m_automaticPetsData);
      }
      
      public function startDevour() : void
      {
         var _loc1_:uint = getAbleGetExpFromDevourPets();
         if(_loc1_)
         {
            startDevourAnimation();
         }
         else
         {
            m_automaticPetPanel.showWarningBox("请放入要吞噬的妖将",0);
         }
      }
      
      private function endDevour() : void
      {
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         var _loc1_:uint = getAbleGetExpFromDevourPets();
         if(_loc1_)
         {
            m_automaticPetVO.addExp(_loc1_);
            m_automaticPetVO.changeData();
            _loc2_ = int(m_choiceAutomaticPetVOs.length);
            _loc3_ = 0;
            while(_loc3_ < _loc2_)
            {
               m_automaticPetsData.spliceAutomaticPetVO(m_choiceAutomaticPetVOs[_loc3_]);
               ClearUtil.clearObject(m_choiceAutomaticPetVOs[_loc3_]);
               m_choiceAutomaticPetVOs[_loc3_] = null;
               _loc3_++;
            }
            m_choiceAutomaticPetVOs.length = 0;
            empyAddBtns();
            if(m_automaticPetVO.partnerName)
            {
               setData(m_automaticPetVO,m_automaticPetsData);
            }
            forceShowOneVO = m_automaticPetVO;
            m_automaticPetPanel.refreshShow(m_automaticPetVO);
            new MiddleWord(m_show.stage,"妖将经验 +" + _loc1_);
            SubmitFunction.getInstance().setData3(5,m_automaticPetVO.getId(),m_automaticPetVO.getName(),m_automaticPetVO.getLevel());
         }
         else
         {
            m_automaticPetPanel.showWarningBox("请放入要吞噬的妖将",0);
         }
      }
      
      private function empyAddBtns() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = int(m_addBtns.length);
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            m_addBtns[_loc2_].setAutomaticPetVO(null);
            _loc2_++;
         }
      }
      
      private function openChoiceDevourPanel() : void
      {
         if(m_choiceDevourPanel == null)
         {
            m_choiceDevourPanel = new ChoiceDevourPanel();
            m_choiceDevourPanel.init(this);
            m_choiceDevourPanel.setData(m_automaticPetVO,m_automaticPetsData);
         }
         m_automaticPetPanel.addChild(m_choiceDevourPanel);
      }
      
      public function closeChoiceDevourPanel() : void
      {
         ClearUtil.clearObject(m_choiceDevourPanel);
         m_choiceDevourPanel = null;
      }
      
      public function sureChoiceDevourAutomaticPets(param1:Vector.<AutomaticPetVO>) : void
      {
         var _loc4_:int = 0;
         m_choiceAutomaticPetVOs.length = 0;
         empyAddBtns();
         var _loc2_:int = int(param1.length);
         var _loc3_:int = int(m_addBtns.length);
         _loc4_ = 0;
         while(_loc4_ < _loc2_ && _loc4_ < _loc3_)
         {
            m_choiceAutomaticPetVOs.push(param1[_loc4_]);
            m_addBtns[_loc4_].setAutomaticPetVO(param1[_loc4_]);
            _loc4_++;
         }
         if(getAbleGetExpFromDevourPets() && m_automaticPetVO.getLevel() < m_automaticPetVO.getMaxLevel())
         {
            initAttributePartTwoFrame();
         }
      }
      
      private function startDevourAnimation() : void
      {
         var _loc2_:int = 0;
         m_mainDevourAnimation.getDisplayShow().visible = true;
         m_mainDevourAnimation.gotoAndPlay("1");
         var _loc1_:int = int(m_devourAnimations.length);
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            if(m_addBtns[_loc2_].getAutomaticPetVO())
            {
               m_devourAnimations[_loc2_].getDisplayShow().visible = true;
               m_devourAnimations[_loc2_].gotoAndPlay("1");
            }
            _loc2_++;
         }
         MyFunction.getInstance().changeSaturation(m_startBtn.getShow(),-100);
         Part1.getInstance().mouseChildren = false;
         Part1.getInstance().mouseEnabled = false;
      }
      
      private function endDevourAnimation() : void
      {
         var _loc2_:int = 0;
         m_mainDevourAnimation.getDisplayShow().visible = false;
         var _loc1_:int = int(m_devourAnimations.length);
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            m_devourAnimations[_loc2_].getDisplayShow().visible = false;
            _loc2_++;
         }
         MyFunction.getInstance().changeSaturation(m_startBtn.getShow(),0);
         Part1.getInstance().mouseChildren = true;
         Part1.getInstance().mouseEnabled = true;
         endDevour();
      }
      
      private function devourAnimatonStop(param1:AnimationShowPlayLogicShell) : void
      {
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         if(param1 != m_mainDevourAnimation)
         {
            _loc2_ = int(m_devourAnimations.length);
            _loc3_ = 0;
            while(_loc3_ < _loc2_)
            {
               if(m_devourAnimations[_loc3_] == param1)
               {
                  m_addBtns[_loc3_].setAutomaticPetVO(null);
               }
               _loc3_++;
            }
         }
         else
         {
            endDevourAnimation();
         }
      }
      
      public function getPetPanel() : AutomaticPetPanel
      {
         return m_automaticPetPanel;
      }
   }
}

