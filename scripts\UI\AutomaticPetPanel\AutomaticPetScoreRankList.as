package UI.AutomaticPetPanel
{
   import UI.MyFunction2;
   import UI.PKUI.RankListPanel;
   import flash.display.MovieClip;
   
   public class AutomaticPetScoreRankList extends RankListPanel
   {
      public function AutomaticPetScoreRankList()
      {
         super();
         _isShowAutomatic = true;
      }
      
      override public function init(param1:int, param2:Number) : void
      {
         _rankId = param1;
         m_show = MyFunction2.returnShowByClassName("AutoPetScoreRankList") as MovieClip;
         addChild(m_show);
         super.init(param1,param2);
      }
   }
}

