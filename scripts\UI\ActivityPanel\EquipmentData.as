package UI.ActivityPanel
{
   import UI.DataManagerParent;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.XMLSingle;
   import YJFY.Utils.ClearUtil;
   
   public class EquipmentData extends DataManagerParent
   {
      private var m_equipmentVO:EquipmentVO;
      
      private var m_num:uint;
      
      public function EquipmentData(param1:uint, param2:uint, param3:String)
      {
         super();
         m_equipmentVO = XMLSingle.getEquipmentVOByID(param1,XMLSingle.getInstance().equipmentXML,param3,false);
         this.num = param2;
      }
      
      override public function clear() : void
      {
         super.clear();
         ClearUtil.clearObject(m_equipmentVO);
         m_equipmentVO = null;
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.num = m_num;
      }
      
      public function getNum() : uint
      {
         return num;
      }
      
      public function getEquipmentVO() : EquipmentVO
      {
         return m_equipmentVO;
      }
      
      private function set num(param1:uint) : void
      {
         _antiwear.num = param1;
      }
      
      private function get num() : uint
      {
         return _antiwear.num;
      }
   }
}

