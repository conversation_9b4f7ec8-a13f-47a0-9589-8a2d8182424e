package org.bytearray.gif.player
{
   import flash.display.Bitmap;
   import flash.errors.ScriptTimeoutError;
   import flash.events.Event;
   import flash.events.IOErrorEvent;
   import flash.events.TimerEvent;
   import flash.net.URLLoader;
   import flash.net.URLRequest;
   import flash.utils.ByteArray;
   import flash.utils.Timer;
   import org.bytearray.gif.decoder.GIFDecoder;
   import org.bytearray.gif.errors.FileTypeError;
   import org.bytearray.gif.events.FileTypeEvent;
   import org.bytearray.gif.events.FrameEvent;
   import org.bytearray.gif.events.GIFPlayerEvent;
   import org.bytearray.gif.events.TimeoutEvent;
   import org.bytearray.gif.frames.GIFFrame;
   
   public class GIFPlayer extends Bitmap
   {
      private var urlLoader:URLLoader;
      
      private var gifDecoder:GIFDecoder;
      
      private var aFrames:Array;
      
      private var myTimer:Timer;
      
      private var iInc:int;
      
      private var iIndex:int;
      
      private var auto:Boolean;
      
      private var arrayLng:uint;
      
      public function GIFPlayer(param1:Boolean = true)
      {
         super();
         auto = param1;
         iIndex = iInc = 0;
         myTimer = new Timer(0,0);
         aFrames = [];
         urlLoader = new URLLoader();
         urlLoader.dataFormat = "binary";
         urlLoader.addEventListener("complete",onComplete);
         urlLoader.addEventListener("ioError",onIOError);
         myTimer.addEventListener("timer",update);
         gifDecoder = new GIFDecoder();
      }
      
      private function onIOError(param1:IOErrorEvent) : void
      {
         dispatchEvent(param1);
      }
      
      private function onComplete(param1:Event) : void
      {
         readStream(param1.target.data);
      }
      
      private function readStream(param1:ByteArray) : void
      {
         var _loc3_:int = 0;
         var _loc4_:int = 0;
         var _loc2_:* = param1;
         aFrames = [];
         iInc = 0;
         try
         {
            gifDecoder.read(_loc2_);
            _loc3_ = gifDecoder.getFrameCount();
            _loc4_ = 0;
            while(_loc4_ < _loc3_)
            {
               aFrames[_loc4_] = gifDecoder.getFrame(_loc4_);
               _loc4_++;
            }
            arrayLng = aFrames.length;
            auto ? play() : gotoAndStop(1);
            dispatchEvent(new GIFPlayerEvent("complete",aFrames[0].bitmapData.rect));
         }
         catch(e:ScriptTimeoutError)
         {
            dispatchEvent(new TimeoutEvent("timeout"));
         }
         catch(e:FileTypeError)
         {
            dispatchEvent(new FileTypeEvent("invalid"));
         }
         catch(e:Error)
         {
            throw new Error("An unknown error occured, make sure the GIF file contains at least one frame\nNumber of frames : " + aFrames.length);
         }
      }
      
      private function update(param1:TimerEvent) : void
      {
         var _loc2_:int = int(aFrames[int(iIndex = iInc++ % arrayLng)].delay);
         param1.target.delay = _loc2_ > 0 ? _loc2_ : 100;
         switch(gifDecoder.disposeValue - 1)
         {
            case 0:
               if(!iIndex)
               {
                  bitmapData = aFrames[0].bitmapData.clone();
               }
               bitmapData.draw(aFrames[iIndex].bitmapData);
               break;
            case 1:
               bitmapData = aFrames[iIndex].bitmapData;
         }
         dispatchEvent(new FrameEvent("rendered",aFrames[iIndex]));
      }
      
      private function concat(param1:int) : int
      {
         var _loc2_:int = 0;
         bitmapData.lock();
         _loc2_ = 0;
         while(_loc2_ < param1)
         {
            bitmapData.draw(aFrames[_loc2_].bitmapData);
            _loc2_++;
         }
         bitmapData.unlock();
         return _loc2_;
      }
      
      public function load(param1:URLRequest) : void
      {
         stop();
         urlLoader.load(param1);
      }
      
      public function loadBytes(param1:ByteArray) : void
      {
         readStream(param1);
      }
      
      public function play() : void
      {
         if(aFrames.length > 0)
         {
            if(!myTimer.running)
            {
               myTimer.start();
            }
            return;
         }
         throw new Error("Nothing to play");
      }
      
      public function stop() : void
      {
         if(myTimer.running)
         {
            myTimer.stop();
         }
      }
      
      public function get currentFrame() : int
      {
         return iIndex + 1;
      }
      
      public function get totalFrames() : int
      {
         return aFrames.length;
      }
      
      public function get loopCount() : int
      {
         return gifDecoder.getLoopCount();
      }
      
      public function get autoPlay() : Boolean
      {
         return auto;
      }
      
      public function get frames() : Array
      {
         return aFrames;
      }
      
      public function gotoAndStop(param1:int) : void
      {
         if(param1 >= 1 && param1 <= aFrames.length)
         {
            if(param1 == currentFrame)
            {
               return;
            }
            iIndex = iInc = param1 - 1;
            switch(gifDecoder.disposeValue - 1)
            {
               case 0:
                  bitmapData = aFrames[0].bitmapData.clone();
                  bitmapData.draw(aFrames[concat(iInc)].bitmapData);
                  break;
               case 1:
                  bitmapData = aFrames[iInc].bitmapData;
            }
            if(myTimer.running)
            {
               myTimer.stop();
            }
            return;
         }
         throw new RangeError("Frame out of range, please specify a frame between 1 and " + aFrames.length);
      }
      
      public function gotoAndPlay(param1:int) : void
      {
         if(param1 >= 1 && param1 <= aFrames.length)
         {
            if(param1 == currentFrame)
            {
               return;
            }
            iIndex = iInc = param1 - 1;
            switch(gifDecoder.disposeValue - 1)
            {
               case 0:
                  bitmapData = aFrames[0].bitmapData.clone();
                  bitmapData.draw(aFrames[concat(iInc)].bitmapData);
                  break;
               case 1:
                  bitmapData = aFrames[iInc].bitmapData;
            }
            if(!myTimer.running)
            {
               myTimer.start();
            }
            return;
         }
         throw new RangeError("Frame out of range, please specify a frame between 1 and " + aFrames.length);
      }
      
      public function getFrame(param1:int) : GIFFrame
      {
         var _loc2_:* = null;
         if(param1 >= 1 && param1 <= aFrames.length)
         {
            return aFrames[param1 - 1];
         }
         throw new RangeError("Frame out of range, please specify a frame between 1 and " + aFrames.length);
      }
      
      public function getDelay(param1:int) : int
      {
         var _loc2_:int = 0;
         if(param1 >= 1 && param1 <= aFrames.length)
         {
            return int(aFrames[param1 - 1].delay);
         }
         throw new RangeError("Frame out of range, please specify a frame between 1 and " + aFrames.length);
      }
      
      public function dispose() : void
      {
         var _loc2_:int = 0;
         stop();
         var _loc1_:int = int(aFrames.length);
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            aFrames[_loc2_].bitmapData.dispose();
            _loc2_++;
         }
      }
   }
}

