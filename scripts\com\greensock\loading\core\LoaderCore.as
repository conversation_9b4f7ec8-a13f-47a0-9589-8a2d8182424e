package com.greensock.loading.core
{
   import com.greensock.events.LoaderEvent;
   import com.greensock.loading.LoaderMax;
   import flash.display.DisplayObject;
   import flash.events.Event;
   import flash.events.EventDispatcher;
   import flash.events.ProgressEvent;
   import flash.net.LocalConnection;
   import flash.system.Capabilities;
   import flash.utils.Dictionary;
   import flash.utils.getTimer;
   
   public class <PERSON>ader<PERSON>ore extends EventDispatcher
   {
      public static const version:Number = 1.77;
      
      protected static var _loaderCount:uint = 0;
      
      protected static var _isLocal:Boolean;
      
      protected static var _globalRootLoader:LoaderMax;
      
      protected static var _rootLookup:Dictionary = new Dictionary(false);
      
      protected static var _listenerTypes:Object = {
         "onOpen":"open",
         "onInit":"init",
         "onComplete":"complete",
         "onProgress":"progress",
         "onCancel":"cancel",
         "onFail":"fail",
         "onError":"error",
         "onSecurityError":"securityError",
         "onHTTPStatus":"httpStatus",
         "onIOError":"ioError",
         "onScriptAccessDenied":"scriptAccessDenied",
         "onChildOpen":"childOpen",
         "onChildCancel":"childCancel",
         "onChildComplete":"childComplete",
         "onChildProgress":"childProgress",
         "onChildFail":"childFail",
         "onRawLoad":"rawLoad"
      };
      
      protected static var _types:Object = {};
      
      protected static var _extensions:Object = {};
      
      protected var _cachedBytesLoaded:uint;
      
      protected var _cachedBytesTotal:uint;
      
      protected var _status:int;
      
      protected var _prePauseStatus:int;
      
      protected var _dispatchProgress:Boolean;
      
      protected var _rootLoader:LoaderMax;
      
      protected var _cacheIsDirty:Boolean;
      
      protected var _auditedSize:Boolean;
      
      protected var _dispatchChildProgress:Boolean;
      
      protected var _type:String;
      
      protected var _time:uint;
      
      protected var _content:*;
      
      public var vars:Object;
      
      public var name:String;
      
      public var autoDispose:Boolean;
      
      public function LoaderCore(param1:Object = null)
      {
         super();
         this.vars = param1 != null ? param1 : {};
         if(this.vars.isGSVars)
         {
            this.vars = this.vars.vars;
         }
         this.name = this.vars.name != undefined && String(this.vars.name) != "" ? this.vars.name : "loader" + _loaderCount++;
         _cachedBytesLoaded = 0;
         _cachedBytesTotal = uint(this.vars.estimatedBytes) != 0 ? uint(this.vars.estimatedBytes) : LoaderMax.defaultEstimatedBytes;
         this.autoDispose = this.vars.autoDispose == true;
         _status = this.vars.paused == true ? 3 : 0;
         _auditedSize = uint(this.vars.estimatedBytes) != 0 && this.vars.auditSize != true;
         _rootLoader = this.vars.requireWithRoot is DisplayObject ? _rootLookup[this.vars.requireWithRoot] : _globalRootLoader;
         if(_globalRootLoader == null)
         {
            if(this.vars.__isRoot == true)
            {
               return;
            }
            _globalRootLoader = _rootLoader = new LoaderMax({
               "name":"root",
               "__isRoot":true
            });
            _isLocal = new LocalConnection().domain == "localhost" || Capabilities.playerType == "Desktop";
         }
         if(_rootLoader)
         {
            _rootLoader.append(this);
         }
         else
         {
            _rootLookup[this.vars.requireWithRoot] = _rootLoader = new LoaderMax();
            _rootLoader.name = "subloaded_swf_" + this.vars.requireWithRoot.loaderInfo.url;
            _rootLoader.append(this);
         }
         for(var _loc2_ in _listenerTypes)
         {
            if(_loc2_ in this.vars && this.vars[_loc2_] is Function)
            {
               this.addEventListener(_listenerTypes[_loc2_],this.vars[_loc2_],false,0,true);
            }
         }
      }
      
      protected static function _activateClass(param1:String, param2:Class, param3:String) : Boolean
      {
         _types[param1.toLowerCase()] = param2;
         var _loc4_:Array = param3.split(",");
         var _loc5_:int = int(_loc4_.length);
         while(true)
         {
            _loc5_--;
            if(_loc5_ <= -1)
            {
               break;
            }
            _extensions[_loc4_[_loc5_]] = param2;
         }
         return true;
      }
      
      public function load(param1:Boolean = false) : void
      {
         var _loc2_:uint = uint(getTimer());
         if(this.status == 3)
         {
            _status = _prePauseStatus <= 1 ? 0 : _prePauseStatus;
            if(_status == 0 && this is LoaderMax)
            {
               _loc2_ -= _time;
            }
         }
         if(param1 || _status == 4)
         {
            _dump(1,0);
         }
         if(_status == 0)
         {
            _status = 1;
            _time = _loc2_;
            _load();
            if(this.progress < 1)
            {
               dispatchEvent(new LoaderEvent("open",this));
            }
         }
         else if(_status == 2)
         {
            _completeHandler(null);
         }
      }
      
      protected function _load() : void
      {
      }
      
      public function pause() : void
      {
         this.paused = true;
      }
      
      public function resume() : void
      {
         this.paused = false;
         load(false);
      }
      
      public function cancel() : void
      {
         if(_status == 1)
         {
            _dump(0,0);
         }
      }
      
      protected function _dump(param1:int = 0, param2:int = 0, param3:Boolean = false) : void
      {
         _content = null;
         var _loc5_:* = _status == 1;
         if(_status == 3 && param2 != 3 && param2 != 4)
         {
            _prePauseStatus = param2;
         }
         else if(_status != 5)
         {
            _status = param2;
         }
         if(_loc5_)
         {
            _time = getTimer() - _time;
         }
         if(_dispatchProgress && !param3 && _status != 5)
         {
            if(this is LoaderMax)
            {
               _calculateProgress();
            }
            else
            {
               _cachedBytesLoaded = 0;
            }
            dispatchEvent(new LoaderEvent("progress",this));
         }
         if(!param3)
         {
            if(_loc5_)
            {
               dispatchEvent(new LoaderEvent("cancel",this));
            }
            if(param1 != 2)
            {
               dispatchEvent(new LoaderEvent("unload",this));
            }
         }
         if(param2 == 5)
         {
            if(!param3)
            {
               dispatchEvent(new Event("dispose"));
            }
            for(var _loc4_ in _listenerTypes)
            {
               if(_loc4_ in this.vars && this.vars[_loc4_] is Function)
               {
                  this.removeEventListener(_listenerTypes[_loc4_],this.vars[_loc4_]);
               }
            }
         }
      }
      
      public function unload() : void
      {
         _dump(1,0);
      }
      
      public function dispose(param1:Boolean = false) : void
      {
         _dump(param1 ? 3 : 2,5);
      }
      
      public function prioritize(param1:Boolean = true) : void
      {
         dispatchEvent(new Event("prioritize"));
         if(param1 && _status != 2 && _status != 1)
         {
            load(false);
         }
      }
      
      override public function addEventListener(param1:String, param2:Function, param3:Boolean = false, param4:int = 0, param5:Boolean = false) : void
      {
         if(param1 == "progress")
         {
            _dispatchProgress = true;
         }
         else if(param1 == "childProgress" && this is LoaderMax)
         {
            _dispatchChildProgress = true;
         }
         super.addEventListener(param1,param2,param3,param4,param5);
      }
      
      protected function _calculateProgress() : void
      {
      }
      
      public function auditSize() : void
      {
      }
      
      override public function toString() : String
      {
         return _type + " \'" + this.name + "\'" + (this is LoaderItem ? " (" + (this as LoaderItem).url + ")" : "");
      }
      
      protected function _progressHandler(param1:Event) : void
      {
         if(param1 is ProgressEvent)
         {
            _cachedBytesLoaded = (param1 as ProgressEvent).bytesLoaded;
            _cachedBytesTotal = (param1 as ProgressEvent).bytesTotal;
            if(!_auditedSize)
            {
               _auditedSize = true;
               dispatchEvent(new Event("auditedSize"));
            }
         }
         if(_dispatchProgress && _status == 1 && _cachedBytesLoaded != _cachedBytesTotal)
         {
            dispatchEvent(new LoaderEvent("progress",this));
         }
      }
      
      protected function _completeHandler(param1:Event = null) : void
      {
         _cachedBytesLoaded = _cachedBytesTotal;
         if(_status != 2)
         {
            dispatchEvent(new LoaderEvent("progress",this));
            _status = 2;
            _time = getTimer() - _time;
         }
         dispatchEvent(new LoaderEvent("complete",this));
         if(this.autoDispose)
         {
            dispose();
         }
      }
      
      protected function _errorHandler(param1:Event) : void
      {
         var _loc3_:Object = param1 is LoaderEvent && this.hasOwnProperty("getChildren") ? param1.target : this;
         var _loc2_:String = (param1 as Object).text;
         trace("Loading error on " + this.toString() + ": " + _loc2_);
         if(param1.type != "error" && this.hasEventListener(param1.type))
         {
            dispatchEvent(new LoaderEvent(param1.type,_loc3_,_loc2_));
         }
         if(this.hasEventListener("error"))
         {
            dispatchEvent(new LoaderEvent("error",_loc3_,this.toString() + " > " + _loc2_));
         }
      }
      
      protected function _failHandler(param1:Event) : void
      {
         _dump(0,4);
         _errorHandler(param1);
         dispatchEvent(new LoaderEvent("fail",param1 is LoaderEvent && this.hasOwnProperty("getChildren") ? param1.target : this,this.toString() + " > " + (param1 as Object).text));
      }
      
      protected function _passThroughEvent(param1:Event) : void
      {
         var _loc3_:String = param1.type;
         var _loc2_:Object = this;
         if(this.hasOwnProperty("getChildren"))
         {
            if(param1 is LoaderEvent)
            {
               _loc2_ = param1.target;
            }
            if(_loc3_ == "complete")
            {
               _loc3_ = "childComplete";
            }
            else if(_loc3_ == "open")
            {
               _loc3_ = "childOpen";
            }
            else if(_loc3_ == "cancel")
            {
               _loc3_ = "childCancel";
            }
            else if(_loc3_ == "fail")
            {
               _loc3_ = "childFail";
            }
         }
         if(this.hasEventListener(_loc3_))
         {
            dispatchEvent(new LoaderEvent(_loc3_,_loc2_,!!param1.hasOwnProperty("text") ? (param1 as Object).text : ""));
         }
      }
      
      public function get paused() : Boolean
      {
         return _status == 3;
      }
      
      public function set paused(param1:Boolean) : void
      {
         if(param1 && _status != 3)
         {
            _prePauseStatus = _status;
            if(_status == 1)
            {
               _dump(0,3);
            }
            _status == 3;
         }
         else if(!param1 && _status == 3)
         {
            if(_prePauseStatus == 1)
            {
               load(false);
            }
            else
            {
               _status = _prePauseStatus || false;
            }
         }
      }
      
      public function get status() : int
      {
         return _status;
      }
      
      public function get bytesLoaded() : uint
      {
         if(_cacheIsDirty)
         {
            _calculateProgress();
         }
         return _cachedBytesLoaded;
      }
      
      public function get bytesTotal() : uint
      {
         if(_cacheIsDirty)
         {
            _calculateProgress();
         }
         return _cachedBytesTotal;
      }
      
      public function get progress() : Number
      {
         return this.bytesTotal != 0 ? _cachedBytesLoaded / _cachedBytesTotal : (_status == 2 ? 1 : 0);
      }
      
      public function get rootLoader() : LoaderMax
      {
         return _rootLoader;
      }
      
      public function get content() : *
      {
         return _content;
      }
      
      public function get auditedSize() : Boolean
      {
         return _auditedSize;
      }
      
      public function get loadTime() : Number
      {
         if(_status == 0)
         {
            return 0;
         }
         if(_status == 1)
         {
            return (getTimer() - _time) / 1000;
         }
         return _time / 1000;
      }
   }
}

